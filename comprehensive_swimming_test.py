#!/usr/bin/env python3

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from qdrant_client import AsyncQdrantClient, models
from multi_tool_agent.config import AgentConfig

async def comprehensive_swimming_test():
    """Comprehensive test to find all Swimming Level 02 activities"""

    client = AsyncQdrantClient(
        url=AgentConfig.QDRANT_URL,
        api_key=AgentConfig.QDRANT_API_KEY,
    )

    try:
        print("🏊 COMPREHENSIVE SWIMMING LEVEL 02 TEST")
        print("="*60)
        
        # Get total count first
        collection_info = await client.get_collection(AgentConfig.QDRANT_COLLECTION_NAME)
        print(f"Total activities in database: {collection_info.points_count}")
        
        # Search for ALL Swimming Level 02 activities
        print(f"\nSearching for ALL Swimming Level 02 activities...")

        all_swimming_02 = []
        offset = None

        while True:
            response = await client.scroll(
                collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
                scroll_filter=models.Filter(
                    must=[
                        models.FieldCondition(key="name", match=models.MatchText(text="Swimming Level 02"))
                    ]
                ),
                limit=100,  # Get 100 at a time
                offset=offset,
                with_payload=True
            )

            points, next_offset = response
            all_swimming_02.extend(points)
            print(f"  Batch: found {len(points)} activities (total so far: {len(all_swimming_02)})")

            if next_offset is None:
                break
            offset = next_offset

        print(f"\n✅ FOUND {len(all_swimming_02)} Swimming Level 02 activities TOTAL")
        print(f"📊 Expected: ~100-150 activities (typically fewer than Level 01)")
        if len(all_swimming_02) > 0:
            print(f"📈 Good coverage achieved!")
        
        if len(all_swimming_02) == 0:
            print("❌ NO ACTIVITIES FOUND - This indicates a serious issue!")
            return

        # Analyze by subcategory
        print(f"\n📊 BREAKDOWN BY SUBCATEGORY:")
        subcategories = {}
        facilities = {}
        cities = {}

        for point in all_swimming_02:
            payload = point.payload
            name = payload.get('name', '')
            facility = payload.get('facility', 'Unknown')
            city = payload.get('city', 'Unknown')
            
            # Extract subcategory from name
            if 'Preschool' in name:
                subcategory = 'Preschool'
            elif 'Children' in name:
                subcategory = 'Children'
            elif 'Tots' in name:
                subcategory = 'Tots - Beginner'
            elif 'Adult and Youth' in name:
                subcategory = 'Adult and Youth'
            elif 'Caregiver Participation' in name:
                subcategory = 'Caregiver Participation'
            else:
                subcategory = 'Other'
            
            subcategories[subcategory] = subcategories.get(subcategory, 0) + 1
            facilities[facility] = facilities.get(facility, 0) + 1
            cities[city] = cities.get(city, 0) + 1
        
        for subcategory, count in sorted(subcategories.items()):
            print(f"  {subcategory}: {count} activities")
        
        print(f"\n🏢 BREAKDOWN BY FACILITY:")
        for facility, count in sorted(facilities.items()):
            print(f"  {facility}: {count} activities")
        
        print(f"\n🏙️ BREAKDOWN BY CITY:")
        for city, count in sorted(cities.items()):
            print(f"  {city}: {count} activities")
        
        # Show sample activities
        print(f"\n📋 SAMPLE ACTIVITIES (first 10):")
        for i, point in enumerate(all_swimming_02[:10]):
            payload = point.payload
            print(f"\n{i+1}. {payload.get('name')}")
            print(f"   City: {payload.get('city')}")
            print(f"   Facility: {payload.get('facility')}")
            print(f"   Ages: {payload.get('min_age_years')}-{payload.get('max_age_years')}")
            print(f"   Start Date: {payload.get('start_date')}")
            print(f"   Price: ${payload.get('price_numeric', 'N/A')}")
        
        # Test age-based filtering
        print(f"\n👶 TESTING AGE-BASED FILTERING (5-year-olds):")
        response_5yo = await client.scroll(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            scroll_filter=models.Filter(
                must=[
                    models.FieldCondition(key="name", match=models.MatchText(text="Swimming Level 02")),
                    models.FieldCondition(key="min_age_years", range=models.Range(lte=5)),
                    models.FieldCondition(key="max_age_years", range=models.Range(gte=5))
                ]
            ),
            limit=50,
            with_payload=True
        )

        print(f"Found {len(response_5yo[0])} Swimming Level 02 activities suitable for 5-year-olds")
        
        # Summary
        print(f"\n🎯 FINAL ASSESSMENT:")
        if len(all_swimming_02) >= 100:
            print(f"🎉 EXCELLENT: Found {len(all_swimming_02)} Swimming Level 02 activities!")
            print(f"✅ Ready for comprehensive agent testing")
        elif len(all_swimming_02) >= 50:
            print(f"✅ GOOD: Found {len(all_swimming_02)} activities - good coverage!")
            print(f"✅ Should provide good agent responses")
        elif len(all_swimming_02) >= 25:
            print(f"✅ DECENT: Found {len(all_swimming_02)} activities - reasonable coverage")
            print(f"✅ Should work for basic queries")
        else:
            print(f"⚠️  LIMITED: Found only {len(all_swimming_02)} activities")
            print(f"🔍 May need investigation")

        print(f"\n💡 NEXT STEPS:")
        if len(all_swimming_02) >= 25:
            print(f"✅ Test agent query: 'any swimming level 2 classes for 5 year olds in new west?'")
            print(f"✅ Should return Swimming Level 02 options with progression info")
        else:
            print(f"🔍 Investigate why Level 02 activities are missing from database")
            print(f"🔍 Check if Level 02 classes exist in source data")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(comprehensive_swimming_test())
