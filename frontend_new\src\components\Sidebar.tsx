import React, { useState } from 'react';
import { Brain, Calendar, RefreshCw, Loader2, X, MessageCircle, User, Activity } from 'lucide-react';
import { useMemoryData } from '../hooks/useMemoryData';
import { useAppStore } from '../stores/appStore';
import { CalendarPanel } from './CalendarPanel';

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
}

type TabType = 'chat' | 'memory' | 'activities' | 'calendar';

export const Sidebar: React.FC<SidebarProps> = ({ isOpen, onToggle }) => {
  const [activeTab, setActiveTab] = useState<TabType>('chat');
  const { sessionId } = useAppStore();

  const tabs = [
    { id: 'chat' as TabType, label: 'Chat', icon: MessageCircle },
    { id: 'memory' as TabType, label: 'Memory', icon: User },
    { id: 'activities' as TabType, label: 'Activities', icon: Activity },
    { id: 'calendar' as TabType, label: 'Calendar', icon: Calendar },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'chat':
        return (
          <div className="p-4">
            <h3 className="font-medium text-gray-900 mb-3">Conversation</h3>
            <p className="text-sm text-gray-600 mb-4">
              This is your main chat with the BC Activity Assistant. I can help you find activities, 
              store preferences, and remember your family's needs.
            </p>
            <div className="space-y-2">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <p className="text-xs text-blue-700">
                  <strong>💬 Try asking:</strong> "Find swimming lessons for my 6-year-old in Vancouver"
                </p>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <p className="text-xs text-green-700">
                  <strong>🧠 I remember:</strong> Family details, activity preferences, and registration history
                </p>
              </div>
            </div>
          </div>
        );

      case 'memory':
        return <MemoryPanel sessionId={sessionId} />;

      case 'activities':
        return <ActivityCardsContainer />;

      case 'calendar':
        return <CalendarPanel sessionId={sessionId} />;

      default:
        return null;
    }
  };

  return (
    <>
      {/* Mobile backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onToggle}
        />
      )}

      {/* Sidebar */}
      <div
        className={`fixed right-0 top-0 h-full w-80 bg-white border-l border-gray-200 transform transition-transform duration-300 ease-in-out z-50 ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Assistant Panel</h2>
            <button
              onClick={onToggle}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              title="Close sidebar"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <nav className="flex">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 flex items-center justify-center space-x-2 px-3 py-3 text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <tab.icon className="w-4 h-4" />
                  <span className="hidden sm:inline">{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="h-[calc(100vh-120px)] overflow-y-auto">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </>
  );
};

function MemoryPanel({ sessionId }: { sessionId: string }) {
  const { 
    memoryData, 
    loading, 
    error, 
    source, 
    refreshMemoryData, 
    hasChildren, 
    hasPreferences, 
    hasActivities, 
    hasFacts,
    isRealData 
  } = useMemoryData(sessionId);

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="animate-spin text-blue-500" size={32} />
          <span className="ml-2 text-gray-600">Loading memory...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <h3 className="font-semibold text-gray-900">Family Memory</h3>
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="text-sm text-red-600">Error loading memory: {error}</div>
          <button 
            onClick={refreshMemoryData}
            className="mt-2 text-xs text-red-500 hover:text-red-700"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-4">
      {/* Header with Data Source Indicator */}
      <div className="flex items-center justify-between">
        <h3 className="font-semibold text-gray-900">Family Memory</h3>
        <div className="flex items-center space-x-2">
          <div className={`text-xs px-2 py-1 rounded ${
            isRealData 
              ? 'bg-green-100 text-green-700' 
              : 'bg-yellow-100 text-yellow-700'
          }`}>
            {isRealData ? 'Live Data' : source === 'mock' ? 'Mock Data' : 'Fallback'}
          </div>
          <button
            onClick={refreshMemoryData}
            className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            title="Refresh memory data"
          >
            <RefreshCw size={14} />
          </button>
        </div>
      </div>
      
      {/* Children Profiles */}
      {hasChildren ? (
        <div>
          <h4 className="font-medium text-sm text-gray-700 mb-3 flex items-center">
            <span className="mr-2">👨‍👩‍👧‍👦</span>
            Children ({memoryData!.children.length})
          </h4>
          <div className="space-y-2">
            {memoryData!.children.map((child, index) => (
              <div key={index} className="p-3 bg-blue-50 rounded-lg">
                <div className="font-medium text-sm">{child.name}</div>
                {child.age > 0 && (
                  <div className="text-xs text-gray-600">Age: {child.age}</div>
                )}
                {child.interests.length > 0 && (
                  <div className="text-xs text-gray-600 mt-1">
                    Interests: {child.interests.join(', ')}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      ) : null}

      {/* Preferences */}
      {hasPreferences ? (
        <div>
          <h4 className="font-medium text-sm text-gray-700 mb-3 flex items-center">
            <span className="mr-2">⚙️</span>
            Preferences ({memoryData!.preferences.length})
          </h4>
          <div className="space-y-2">
            {memoryData!.preferences.map((pref, index) => (
              <div key={index} className="p-3 bg-green-50 rounded-lg">
                <div className="font-medium text-sm">{pref.type}</div>
                <div className="text-xs text-gray-600">{pref.value}</div>
                <div className={`text-xs mt-1 ${
                  pref.strength === 'strong' ? 'text-green-600' :
                  pref.strength === 'medium' ? 'text-yellow-600' : 'text-gray-500'
                }`}>
                  {pref.strength} preference
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : null}

      {/* Recent Activities */}
      {hasActivities ? (
        <div>
          <h4 className="font-medium text-sm text-gray-700 mb-3 flex items-center">
            <span className="mr-2">🏃‍♀️</span>
            Recent Activities ({memoryData!.recentActivities.length})
          </h4>
          <div className="space-y-2">
            {memoryData!.recentActivities.map((activity, index) => (
              <div key={index} className="p-3 bg-purple-50 rounded-lg">
                <div className="font-medium text-sm">{activity.name}</div>
                <div className={`text-xs ${
                  activity.status === 'completed' ? 'text-green-600' :
                  activity.status === 'registered' ? 'text-blue-600' : 'text-gray-600'
                }`}>
                  {activity.status}
                </div>
                {activity.date && (
                  <div className="text-xs text-gray-500 mt-1">{activity.date}</div>
                )}
                {activity.satisfaction && (
                  <div className="text-xs text-gray-600 mt-1">"{activity.satisfaction}"</div>
                )}
              </div>
            ))}
          </div>
        </div>
      ) : null}

      {/* Stored Facts */}
      {hasFacts ? (
        <div>
          <h4 className="font-medium text-sm text-gray-700 mb-3 flex items-center">
            <span className="mr-2">💡</span>
            Stored Facts ({memoryData!.storedFacts.length})
          </h4>
          <div className="space-y-2">
            {memoryData!.storedFacts.map((fact, index) => (
              <div key={index} className="p-3 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-700">{fact}</div>
              </div>
            ))}
          </div>
        </div>
      ) : null}

      {/* Empty State */}
      {!hasChildren && !hasPreferences && !hasActivities && !hasFacts && (
        <div className="text-center py-8 text-gray-500">
          <Brain className="w-8 h-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">No family information stored yet</p>
          <p className="text-xs">Information will appear as you chat with me</p>
          {!isRealData && (
            <p className="text-xs mt-2 text-yellow-600">
              Using {source} data - check server connection
            </p>
          )}
        </div>
      )}
    </div>
  );
}

function ActivityCardsContainer() {
  return (
    <div className="space-y-4 p-4">
      <h3 className="font-semibold text-gray-900">Activity Cards</h3>
      <div className="text-center py-8 text-gray-500">
        <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
        <p className="text-sm">No activities found yet</p>
        <p className="text-xs">Activity cards will appear when I find BC programs for you</p>
      </div>
    </div>
  );
}

export default Sidebar; 