from __future__ import annotations

"""
Optimized and Fast Drop-in Activity Scraper for PerfectMind.
"""

import asyncio
import json
import logging
import math
import re
import time
import uuid
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional

import httpx
from bs4 import BeautifulSoup

# --- Configuration ---
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

BASE_URL = "https://cityofnewwestminster.perfectmind.com"
ORG_ID = "23693"
WIDGET_ID = "50a33660-b4f7-44d9-9256-e10effec8641"
NAMESPACE_UUID = uuid.UUID("9e71ea85-7976-4395-a78b-1616c689eea7")

HTTP_TIMEOUT = 30.0
PAGE_SIZE = 100
CONCURRENT_REQUESTS = 5
MAX_RETRIES = 3
RETRY_DELAY = 2

_cur_dir = Path(__file__).parent


# --- Data Parsing & Expansion Functions ---
def _parse_age_range(age_str: Optional[str]) -> Dict[str, Optional[int]]:
    if not age_str: return {"min_age_years": None, "max_age_years": None}
    age_str = age_str.lower()
    patterns = [
        (re.compile(r"(\d+)\s+and\s+under"), lambda m: {"min_age_years": 0, "max_age_years": int(m.group(1))}),
        (re.compile(r"(\d+)\s*-\s*(\d+)"), lambda m: {"min_age_years": int(m.group(1)), "max_age_years": int(m.group(2))}),
        (re.compile(r"(\d+)\+"), lambda m: {"min_age_years": int(m.group(1)), "max_age_years": 99}),
        (re.compile(r"(\d+)\s+years\s+and\s+over"), lambda m: {"min_age_years": int(m.group(1)), "max_age_years": 99}),
    ]
    for pat, fn in patterns:
        m = pat.search(age_str)
        if m: return fn(m)
    return {"min_age_years": None, "max_age_years": None}

def _parse_time_range(time_str: Optional[str]) -> Dict[str, Any]:
    if not time_str: return {"start_time_iso": None, "end_time_iso": None, "duration_minutes": None}
    parts = [p.strip() for p in re.split(r"to|-", time_str, flags=re.IGNORECASE) if p.strip()]
    if len(parts) < 2: return {"start_time_iso": None, "end_time_iso": None, "duration_minutes": None}
    fmt_in, fmt_out = "%I:%M %p", "%H:%M:%S"
    try:
        start_dt, end_dt = datetime.strptime(parts[0], fmt_in), datetime.strptime(parts[1], fmt_in)
    except ValueError: return {"start_time_iso": None, "end_time_iso": None, "duration_minutes": None}
    dur = (end_dt - start_dt).total_seconds() / 60
    if dur < 0: dur += 24 * 60
    return {"start_time_iso": start_dt.strftime(fmt_out), "end_time_iso": end_dt.strftime(fmt_out), "duration_minutes": int(dur)}

def _parse_price(p: Optional[str]) -> Optional[float]:
    if not p: return None
    m = re.search(r"[\d\.]+", p)
    return float(m.group(0)) if m else None

def _expand_recurring_activity(activity: Dict[str, Any]) -> List[Dict[str, Any]]:
    start_date_str = activity.get("start_date")
    end_date_str = activity.get("end_date")

    if not start_date_str or not end_date_str: return [activity]
    date_fmt = "%d-%b-%Y"
    try:
        start_date = datetime.strptime(start_date_str, date_fmt)
        end_date = datetime.strptime(end_date_str, date_fmt)
    except (ValueError, TypeError): return [activity]

    if start_date > end_date: return [activity]
    
    days_to_check = set()
    api_weekday_masks = activity.get("weekdays_mask")
    if isinstance(api_weekday_masks, list):
        mask_to_weekday = {1: 6, 2: 0, 4: 1, 8: 2, 16: 3, 32: 4, 64: 5}
        for mask_val in api_weekday_masks:
            if mask_val in mask_to_weekday: days_to_check.add(mask_to_weekday[mask_val])
    
    if not days_to_check:
        days_of_week_raw = activity.get("days_of_week") or ""
        weekday_map = {"mon": 0, "tue": 1, "wed": 2, "thu": 3, "fri": 4, "sat": 5, "sun": 6}
        day_tokens = {t[:3] for t in re.findall(r"[A-Za-z]+", days_of_week_raw.lower())}
        days_to_check.update(weekday_map[t] for t in day_tokens if t in weekday_map)

    run_every_day = not days_to_check
    occurrences = []
    current, delta = start_date, timedelta(days=1)
    base_record_id = activity.get("record_id") or str(uuid.uuid4())

    while current <= end_date:
        if run_every_day or current.weekday() in days_to_check:
            occ = activity.copy()
            occ_date_str = current.strftime(date_fmt)
            day_name = current.strftime("%A").lower() # Get full day name e.g., "monday"
            
            occ["start_date"] = occ_date_str
            occ["end_date"] = occ_date_str
            occ["day_of_week"] = current.strftime("%a").lower() # This can be kept if used internally
            occ["days_of_week_list"] = [day_name] # Add the standardized list
            occ["record_id"] = str(uuid.uuid5(NAMESPACE_UUID, f"{base_record_id}-{occ_date_str}"))
            occurrences.append(occ)
        current += delta
    return occurrences if occurrences else [activity]

def _parse_event_date(date_str: Optional[str]) -> Dict[str, Optional[str]]:
    if not date_str: return {"start_date": None, "end_date": None}
    parts = [p.strip() for p in date_str.split(' - ')]
    return {"start_date": parts[0], "end_date": parts[1]} if len(parts) == 2 else {"start_date": date_str, "end_date": date_str}

def _summarize(act: Dict[str, Any]) -> Dict[str, Any]:
    orig_id = act.get("RecordId")
    uniq_key = f"{act.get('ServiceId')}-{act.get('RecurrenceId') or orig_id}"
    date_info = _parse_event_date(act.get("EventDate"))
    start_date, end_date = date_info["start_date"], date_info["end_date"]
    if not start_date and act.get("EventCurrentStartInJsFormat"):
        try:
            dt = datetime.strptime(act["EventCurrentStartInJsFormat"], "%Y-%m-%d")
            start_date = end_date = dt.strftime('%d-%b-%Y')
        except (ValueError, TypeError): pass
    if start_date and not end_date: end_date = start_date
    out = {
        "record_id": str(uuid.uuid5(NAMESPACE_UUID, uniq_key)),
        "name": act.get("EventTitle"),
        "activity_url": f"{BASE_URL}/{ORG_ID}/Clients/BookMe4LandingPages/CoursesLandingPage?widgetId={WIDGET_ID}&courseId={orig_id}",
        "description": act.get("EventDescription"), "category": act.get("CalendarCategory"),
        "general_location": act.get("FacilityName"), "location": None, "facility": None,
        "location_id": act.get("LocationId") or act.get("LocationID") or act.get("FacilityId") or act.get("FacilityID"),
        "age_info": act.get("Age"), "start_date": start_date, "end_date": end_date, "start_time": act.get("EventTime"),
        "end_time": act.get("EventTimeEnd"), "days_of_week": act.get("EventDays"), "price": act.get("Price"), "fees": act.get("Fees"),
        "is_full": act.get("IsFull", False), "registration_status": "open" if not act.get("IsRegistrationClosed") else "closed",
        "course_guid": orig_id, "weekdays_mask": act.get("Weekdays"),
        "calendar_booking_type": act.get("CalendarBookingType"), "calendar_name": act.get("CalendarName"),
    }
    out.update(_parse_age_range(out.get("age_info")))
    out.update(_parse_time_range(out.get("start_time")))
    out["price_numeric"] = _parse_price(out.get("price"))
    return out

def _should_expand(activity: Dict[str, Any]) -> bool:
    booking_type = activity.get("calendar_booking_type")
    if booking_type == 2: return True
    if booking_type == 3: return False
    name = (activity.get("name") or "").lower()
    calendar_name = (activity.get("calendar_name") or "").lower()
    return "drop-in" in name or "drop in" in name or "drop-in" in calendar_name

# FIX: New function to parse the reliable date range from the course details page HTML
def _parse_date_range_from_details(soup: BeautifulSoup) -> Optional[Dict[str, str]]:
    """Parses the main date range from the h4 tag on a course page."""
    h4_tag = soup.find('h4', class_='bm-class-date-range')
    if not h4_tag: return None
    
    date_text = h4_tag.get_text(strip=True) # e.g., "04-Jul-2025 - 30-Aug-2025"
    
    # Regex to capture the start and end dates
    match = re.search(r"(\d{2}-[A-Za-z]{3}-\d{4})\s*-\s*(\d{2}-[A-Za-z]{3}-\d{4})", date_text)
    if match:
        start_date_str, end_date_str = match.groups()
        try:
            # Reformat to be consistent with other date parsing
            start_date = datetime.strptime(start_date_str, "%d-%b-%Y").strftime("%d-%b-%Y")
            end_date = datetime.strptime(end_date_str, "%d-%b-%Y").strftime("%d-%b-%Y")
            return {"start_date": start_date, "end_date": end_date}
        except ValueError:
            return None
    return None


class FastDropInScraper:
    API_URL = f"{BASE_URL}/{ORG_ID}/Clients/Planner2/SearchActivitiesForBookMe"
    SEARCH_URL = f"{BASE_URL}/{ORG_ID}/Clients/BookMe4Search"
    WIDGET_URL = f"{BASE_URL}/{ORG_ID}/Clients/BookMe4?widgetId={WIDGET_ID}"

    def __init__(self) -> None:
        self.http_client = httpx.AsyncClient(timeout=HTTP_TIMEOUT, follow_redirects=True, http2=True)
        self.semaphore = asyncio.Semaphore(CONCURRENT_REQUESTS)
        self._csrf: Optional[str] = None
        self.widget_locations, self.widget_calendars = [], []
        self.filter_categories, self.filter_seasons, self.filter_services = [], [], []
        self.location_mapping: Dict[str, str] = {}
        self.location_id_override_mapping: Dict[str, str] = {}
        self.facility_to_location_mapping: Dict[str, str] = {}

    async def aclose(self): await self.http_client.aclose()

    async def _get_csrf(self) -> Optional[str]:
        if self._csrf: return self._csrf
        try:
            r = await self.http_client.get(self.WIDGET_URL)
            r.raise_for_status()
            token_input = BeautifulSoup(r.text, 'html.parser').find('input', {'name': '__RequestVerificationToken'})
            if token_input and 'value' in token_input.attrs:
                self._csrf = token_input['value']
                return self._csrf
        except httpx.RequestError: pass
        return None

    async def _make_request_with_retry(self, method: str, url: str, **kwargs) -> Optional[httpx.Response]:
        for attempt in range(MAX_RETRIES):
            try:
                headers = kwargs.pop("headers", {}); headers.setdefault("X-Requested-With", "XMLHttpRequest")
                resp = await self.http_client.post(url, headers=headers, **kwargs) if method.lower() == "post" else await self.http_client.get(url, headers=headers, **kwargs)
                if resp.status_code < 400: return resp
                if 400 <= resp.status_code < 500 and resp.status_code != 429: return None
            except httpx.RequestError: pass
            await asyncio.sleep(RETRY_DELAY * (2 ** attempt))
        return None

    async def _bootstrap_filters(self) -> bool:
        if not self._csrf: return False
        payload = {"__RequestVerificationToken": self._csrf, "WidgetId": WIDGET_ID, "searchText": "", "searchDropIn": "true", "searchCourses": "true"}
        response = await self._make_request_with_retry("post", self.SEARCH_URL, data=payload)
        if not response: return False
        script_tag = BeautifulSoup(response.text, 'html.parser').find('script', string=re.compile(r'var locations = JSON.parse'))
        if not script_tag or not script_tag.string: return False
        try:
            filter_map = {
                'locations': ('widget_locations', r"var locations = JSON.parse\('(.+?)'\);"),
                'calendars': ('widget_calendars', r"var calendars = JSON.parse\('(.+?)'\);"),
                'categories': ('filter_categories', r"var categories = JSON.parse\('(.+?)'\);"),
                'seasons': ('filter_seasons', r"var seasons = JSON.parse\('(.+?)'\);"),
                'services': ('filter_services', r"var services = JSON.parse\('(.+?)'\);")
            }
            for name, (attr, pattern) in filter_map.items():
                if match := re.search(pattern, script_tag.string):
                    setattr(self, attr, json.loads(match.group(1)))
            for loc in getattr(self, 'widget_locations', []):
                if isinstance(loc, dict) and (val := loc.get('Value')) and loc.get('Text'):
                    self.location_mapping[val] = loc['Text']
            self.facility_to_location_mapping = {
                "Gymnasium (large)": "təməsew̓txʷ Aquatic and Community Centre",
                "Gymnasium (small)": "təməsew̓txʷ Aquatic and Community Centre",
                "Leisure Pool - South": "təməsew̓txʷ Aquatic and Community Centre",
                "Leisure Pool - North": "təməsew̓txʷ Aquatic and Community Centre",
                "Leisure & Lap Pool": "təməsew̓txʷ Aquatic and Community Centre",
                "Lap Pool": "təməsew̓txʷ Aquatic and Community Centre",
                "Fitness Centre": "təməsew̓txʷ Aquatic and Community Centre",
                "Multi-Purpose Room 1": "təməsew̓txʷ Aquatic and Community Centre",
                "Multi-Purpose Room 2": "təməsew̓txʷ Aquatic and Community Centre",
                "Multi-Purpose Room 3": "təməsew̓txʷ Aquatic and Community Centre",
                "Multi-Purpose Room 4": "təməsew̓txʷ Aquatic and Community Centre",
                "Multipurpose 1A": "təməsew̓txʷ Aquatic and Community Centre",
                "Multipurpose 1B": "təməsew̓txʷ Aquatic and Community Centre",
                "Anvil Theatre": "Anvil Centre",
                "Conference Room": "Anvil Centre",
                "Gymnasium": "Centennial Community Centre",
                "Fitness Studio": "Centennial Community Centre",
                "Pool": "Centennial Community Centre",
                "Rink 1": "Moody Park Arena",
                "Rink 2": "Moody Park Arena",
                "Rink": "Queensborough Community Centre",
                "Meeting Room": "Queensborough Community Centre",
                "Outdoor Pool": "Moody Park"
            }
            self.location_id_override_mapping = {
                "681dc09c-0eb8-4179-affa-233e2b24227c": "təməsew̓txʷ Aquatic and Community Centre",
                "771caa56-e1fd-4177-a689-4fae4662e96f": "təməsew̓txʷ Aquatic and Community Centre",
                "56cbf58d-30ed-4dac-afaa-9d6f42453a19": "Moody Park"
            }
        except (json.JSONDecodeError, IndexError):
            return False
        return bool(self.widget_locations)

    async def _fetch_with_payload(self, payload: Dict[str, Any]) -> List[Dict[str, Any]]:
        all_activities = []
        response = await self._make_request_with_retry("post", self.API_URL, json={**payload, "page": 1}, headers={"RequestVerificationToken": self._csrf})
        if not response: return []
        try: data = response.json(); initial_activities = data.get("Activities", []); total_items = data.get("Total", 0)
        except Exception: return []
        if not initial_activities: return []
        all_activities.extend(initial_activities)
        num_pages = min(math.ceil(total_items / PAGE_SIZE), 20)
        page_tasks = [self._make_request_with_retry("post", self.API_URL, json={**payload, "page": i}, headers={"RequestVerificationToken": self._csrf}) for i in range(2, num_pages + 1)]
        for resp in await asyncio.gather(*page_tasks):
            if resp and (data := resp.json()): all_activities.extend(data.get("Activities", []))
        return all_activities

    async def _fetch_and_parse_details(self, activity: Dict[str, Any]) -> Dict[str, Any]:
        if not (class_id := activity.get("course_guid")): return {}
        course_url = f"{BASE_URL}/{ORG_ID}/Clients/BookMe4LandingPages/CoursesLandingPage?widgetId={WIDGET_ID}&courseId={class_id}"
        response = await self._make_request_with_retry("get", course_url)
        if not response: return {}
        details = {}
        try:
            soup = BeautifulSoup(response.text, 'html.parser')
            # Get price
            price_tag = soup.find('div', class_='bm-price-tag')
            if price_tag and (match := re.search(r'\$([\d,]+\.?\d*)', price_tag.get_text(strip=True))):
                price_value = float(match.group(1).replace(',', ''))
                details.update({"price_numeric": price_value, "price": f"${price_value:.2f}", "fees": f"${price_value:.2f}"})
            
            # FIX: Get full date range for courses
            if activity.get("calendar_booking_type") == 3:
                date_range = _parse_date_range_from_details(soup)
                if date_range:
                    details.update(date_range)
        except Exception: pass
        return details

    def _apply_location_mapping(self, activities: List[Dict[str, Any]]) -> None:
        for activity in activities:
            if (loc_id := activity.get("location_id")) and (fac_name := self.location_mapping.get(loc_id)):
                activity["facility"] = activity["location"] = fac_name
            elif (loc_id := activity.get("location_id")) and (fac_name := self.location_id_override_mapping.get(loc_id)):
                 activity["facility"] = activity["location"] = fac_name
            elif (room := activity.get("general_location")) and (fac_name := self.facility_to_location_mapping.get(room)):
                activity["facility"] = activity["location"] = fac_name

    async def scrape(self) -> List[Dict[str, Any]]:
        if not await self._get_csrf() or not await self._bootstrap_filters(): return []
        base_payload = {"widgetId": WIDGET_ID, "searchDropIn": True, "searchCourses": True, "widgetLocations": self.widget_locations, "widgetCalendars": self.widget_calendars, "TimeFilter": 1}
        search_tasks = [self._fetch_with_payload({**base_payload, "searchText": term}) for term in ["", "drop-in", "gym", "fitness", "skate", "swim", "yoga", "sport", "art", "dance"]]
        search_tasks.extend(self._fetch_with_payload({**base_payload, "Category": cat["Value"]}) for cat in self.filter_categories if cat.get("Value") and cat["Value"] != "-1")
        
        all_raw_activities = [act for res in await asyncio.gather(*search_tasks) for act in res if act]
        all_summaries = [_summarize(act) for act in all_raw_activities if act.get("RecordId")]
        
        unique_activities = {s['record_id']: s for s in all_summaries}
        logger.info(f"Found {len(unique_activities)} unique activities after initial summarization.")

        unique_for_details = list(unique_activities.values())
        details_tasks = [self._fetch_and_parse_details(c) for c in unique_for_details]
        details_results = await asyncio.gather(*details_tasks)
        details_map = {course["record_id"]: result for course, result in zip(unique_for_details, details_results)}
        
        for record_id, summary in unique_activities.items():
            if details := details_map.get(record_id):
                summary.update(details)
        
        final_occurrences = []
        for summary in unique_activities.values():
            if _should_expand(summary):
                final_occurrences.extend(_expand_recurring_activity(summary))
            else:
                final_occurrences.append(summary)
        
        self._apply_location_mapping(final_occurrences)
        logger.info(f"Final processed occurrences: {len(final_occurrences)}.")
        return final_occurrences

async def main():
    start_time = time.time()
    scraper = FastDropInScraper()
    try:
        activities = await scraper.scrape()
        out_file = _cur_dir / "fast_dropin_activities.json"
        with open(out_file, "w", encoding="utf-8") as fh:
            json.dump(activities, fh, indent=2, ensure_ascii=False)
        logger.info(f"\n✅ Saved {len(activities)} total activity occurrences to {out_file}")
    finally:
        await scraper.aclose()
        elapsed_time = time.time() - start_time
        logger.info(f"Total execution time: {elapsed_time:.2f}s ({(elapsed_time/60):.2f}m)")

if __name__ == "__main__":
    asyncio.run(main())
