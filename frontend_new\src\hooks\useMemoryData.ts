import { useState, useEffect, useCallback } from 'react';
import { useAppStore } from '../stores/appStore';
import { API_BASE_URL } from '../config';

interface MemoryChild {
  name: string;
  age: number;
  interests: string[];
}

interface MemoryPreference {
  type: string;
  value: string;
  strength: 'strong' | 'medium' | 'weak';
}

interface MemoryActivity {
  name: string;
  status: 'completed' | 'registered' | 'interested';
  satisfaction?: string;
  date?: string;
}

interface MemoryData {
  children: MemoryChild[];
  preferences: MemoryPreference[];
  recentActivities: MemoryActivity[];
  storedFacts: string[];
}

interface MemoryResponse {
  source: 'graphiti' | 'mock' | 'mock_fallback';
  data: MemoryData;
  session_id?: string;
  user_id?: string;
  error?: string;
}

export const useMemoryData = () => {
  const { sessionId } = useAppStore(state => ({ sessionId: state.sessionId }));
  const [memoryData, setMemoryData] = useState<MemoryData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [source, setSource] = useState<string>('unknown');
  
  const { auth } = useAppStore();

  const fetchSummary = useCallback(async () => {
    if (!sessionId) return;
    setLoading(true);
    try {
      const response = await fetch(
        `${API_BASE_URL}/api/memory/summary?session_id=${sessionId}`, 
        {
          headers: { 'Content-Type': 'application/json' }
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result: MemoryResponse = await response.json();
      
      setMemoryData(result.data);
      setSource(result.source);
      
      if (result.error) {
        console.warn('Memory API warning:', result.error);
      }

      console.log(`🧠 Memory data loaded from ${result.source}:`, {
        children: result.data.children.length,
        preferences: result.data.preferences.length,
        activities: result.data.recentActivities.length,
        facts: result.data.storedFacts.length,
        user_id: result.user_id
      });

    } catch (err) {
      console.error('Failed to fetch memory data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      
      // Fallback to mock data on error
      setMemoryData({
        children: [],
        preferences: [],
        recentActivities: [],
        storedFacts: ['Unable to load memory data - check server connection']
      });
      setSource('error_fallback');
    } finally {
      setLoading(false);
    }
  }, [sessionId]);

  // Fetch on mount and when sessionId or auth changes
  useEffect(() => {
    if (sessionId) {
      fetchSummary();
    }
  }, [sessionId, auth.token, fetchSummary]);

  // Listen for memory refresh events from SSE
  useEffect(() => {
    const handleMemoryRefresh = (event: CustomEvent) => {
      if (event.detail.sessionId === sessionId) {
        console.log('🔄 Memory refresh triggered by SSE event');
        fetchSummary();
      }
    };

    window.addEventListener('memoryRefresh', handleMemoryRefresh as EventListener);
    
    return () => {
      window.removeEventListener('memoryRefresh', handleMemoryRefresh as EventListener);
    };
  }, [sessionId, fetchSummary]);

  // Refresh function for manual updates
  const refreshMemoryData = () => {
    fetchSummary();
  };

  return {
    memoryData,
    loading,
    error,
    source,
    refreshMemoryData,
    // Helper functions
    hasChildren: memoryData?.children.length ?? 0 > 0,
    hasPreferences: memoryData?.preferences.length ?? 0 > 0,
    hasActivities: memoryData?.recentActivities.length ?? 0 > 0,
    hasFacts: memoryData?.storedFacts.length ?? 0 > 0,
    isRealData: source === 'graphiti'
  };
};

export type { MemoryData, MemoryChild, MemoryPreference, MemoryActivity }; 