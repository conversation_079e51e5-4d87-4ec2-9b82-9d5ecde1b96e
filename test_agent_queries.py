#!/usr/bin/env python3

import asyncio
import sys
import os

# Add the parent directory to the path to fix imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from multi_tool_agent.orchestrator import root_agent
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.adk.agents.run_config import RunConfig, StreamingMode
from google.genai import types

async def test_agent_queries():
    """Test the agent with the sample queries using the full agent system"""

    print("🤖 TESTING AGENT WITH SAMPLE QUERIES")
    print("="*60)

    # Initialize the agent system
    session_service = InMemorySessionService()
    runner = Runner(agent=root_agent, session_service=session_service, app_name="test_agent")
    user_id = "test_user"
    session_id = "test_session"

    # Test Query 1: Swimming classes for 5-year-olds
    print("\n🏊 TEST QUERY 1: Swimming classes for 5-year-olds")
    print("-" * 50)
    query1 = "any swimming classes for 5 year olds in new west?"
    print(f"Query: '{query1}'")
    print()

    try:
        print("🤖 AGENT RESPONSE:")
        print("=" * 40)

        # Create session first
        await session_service.create_session(user_id=user_id, session_id=session_id, app_name="test_agent")

        content = types.Content(role='user', parts=[types.Part(text=query1)])
        run_config = RunConfig(streaming_mode=StreamingMode.SSE)

        final_response = ""
        async for event in runner.run_async(
            user_id=user_id,
            session_id=session_id,
            new_message=content,
            run_config=run_config
        ):
            if event.is_final_response():
                if event.actions.skip_summarization and event.get_function_responses():
                    final_response = event.get_function_responses()[0].response
                elif event.content and event.content.parts and event.content.parts[0].text:
                    final_response = event.content.parts[0].text.strip()
                break

        print(final_response)
        print("=" * 40)

    except Exception as e:
        print(f"❌ Error in Query 1: {e}")
        import traceback
        traceback.print_exc()

    print("\n" + "="*60)

    # Test Query 2: Back-to-back classes
    print("\n🤸 TEST QUERY 2: Back-to-back classes")
    print("-" * 50)
    query2 = "any back to back classes for 5 year olds in new west?"
    print(f"Query: '{query2}'")
    print()

    try:
        print("🤖 AGENT RESPONSE:")
        print("=" * 40)

        # Create new session for second query
        session_id_2 = session_id + "_2"
        await session_service.create_session(user_id=user_id, session_id=session_id_2, app_name="test_agent")

        content = types.Content(role='user', parts=[types.Part(text=query2)])
        run_config = RunConfig(streaming_mode=StreamingMode.SSE)

        final_response = ""
        async for event in runner.run_async(
            user_id=user_id,
            session_id=session_id_2,
            new_message=content,
            run_config=run_config
        ):
            if event.is_final_response():
                if event.actions.skip_summarization and event.get_function_responses():
                    final_response = event.get_function_responses()[0].response
                elif event.content and event.content.parts and event.content.parts[0].text:
                    final_response = event.content.parts[0].text.strip()
                break

        print(final_response)
        print("=" * 40)

    except Exception as e:
        print(f"❌ Error in Query 2: {e}")
        import traceback
        traceback.print_exc()

    print("\n" + "="*60)
    print("🎯 TESTING COMPLETE")
    print("\nCompare the agent responses above with the expected ideal answers.")
    print("Key things to look for:")
    print("✅ Comprehensive Swimming Levels 01-06 coverage")
    print("✅ Detailed scheduling with days, times, dates")
    print("✅ Both təməsew̓txʷ and Moody Park locations")
    print("✅ Accurate pricing information")
    print("✅ Proper age-appropriate filtering")
    print("✅ Back-to-back class identification and scheduling")

if __name__ == "__main__":
    asyncio.run(test_agent_queries())
