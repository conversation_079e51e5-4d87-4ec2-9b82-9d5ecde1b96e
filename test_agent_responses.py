#!/usr/bin/env python3
"""
Test script to verify our search system and data quality.
"""

import asyncio
import sys
import os
sys.path.append('.')

# Set dummy API key to avoid errors
os.environ['GOOGLE_API_KEY'] = 'dummy'

from multi_tool_agent.tools.activity_search_tools import raw_activity_search

# Mock context
class MockInvocationContext:
    def __init__(self):
        self.user_id = 'test_user'
        self.session = type('Session', (), {'id': 'test_session', 'state': {}})()

from google.adk.tools import ToolContext

async def test_search_data():
    """Test the search system and examine data quality"""

    print("🧪 TESTING SEARCH DATA QUALITY")
    print("="*80)

    context = ToolContext(MockInvocationContext())

    # Test 1: Regular swimming query
    print("\n🏊 TEST 1: Swimming Classes for 5-year-olds in New Westminster")
    print("-" * 60)

    result = await raw_activity_search(
        query='swimming classes',
        filters={'age': 5, 'location': 'new westminster'},
        tool_context=context
    )

    if result['status'] == 'success':
        activities = result['results']
        print(f"✅ Found {len(activities)} activities")

        # Analyze the data structure
        if activities:
            print("\n📋 Sample Activity Analysis:")
            sample = activities[0]

            # Check for key fields needed for the expected response
            key_fields = ['name', 'facility', 'min_age_years', 'max_age_years',
                         'price_numeric', 'start_date', 'end_date', 'days_of_week_list',
                         'start_time_iso', 'end_time_iso']

            print("Field availability:")
            for field in key_fields:
                value = sample.get(field)
                status = "✅" if value is not None and value != '' else "❌"
                print(f"   {status} {field}: {value}")

            print(f"\n📊 Activity Breakdown by Level:")
            level_counts = {}
            for activity in activities:
                name = activity.get('name', '')
                if 'Level' in name:
                    level = name.split('Level')[1].split()[0] if 'Level' in name else 'Unknown'
                    level_counts[level] = level_counts.get(level, 0) + 1

            for level, count in sorted(level_counts.items()):
                print(f"   Level {level}: {count} activities")

        else:
            print("❌ No activities found")
    else:
        print(f"❌ Search failed: {result.get('message', 'Unknown error')}")

    print("\n" + "="*80)

    # Test 2: Back-to-back query
    print("\n🤸 TEST 2: Back-to-Back Classes")
    print("-" * 40)

    result2 = await raw_activity_search(
        query='gymnastics classes',
        filters={'age': 5, 'location': 'new westminster'},
        tool_context=context,
        find_back_to_back=True
    )

    if result2['status'] == 'success':
        activities = result2['results']
        print(f"✅ Found {len(activities)} activities for back-to-back analysis")

        # Look for time patterns that could be back-to-back
        time_slots = {}
        for activity in activities:
            start_time = activity.get('start_time_iso', '')
            end_time = activity.get('end_time_iso', '')
            date = activity.get('start_date', '')
            if start_time and end_time and date:
                key = f"{date}"
                if key not in time_slots:
                    time_slots[key] = []
                time_slots[key].append({
                    'name': activity.get('name', ''),
                    'start': start_time,
                    'end': end_time,
                    'facility': activity.get('facility', '')
                })

        print(f"\n📅 Time slot analysis: {len(time_slots)} unique dates")
        for date, slots in list(time_slots.items())[:3]:  # Show first 3 dates
            print(f"   {date}: {len(slots)} activities")

    else:
        print(f"❌ Back-to-back search failed: {result2.get('message', 'Unknown error')}")

    print("\n🏁 TESTING COMPLETE")
    print("\n💡 INSIGHTS:")
    print("- Check if all required fields are populated in the database")
    print("- Verify that New Westminster data includes detailed scheduling info")
    print("- Ensure the ActivitySynthesizerAgent can access all needed fields")

if __name__ == "__main__":
    asyncio.run(test_search_data())
