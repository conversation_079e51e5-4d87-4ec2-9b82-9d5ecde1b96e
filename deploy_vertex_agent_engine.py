"""Deploy the existing root_agent to Vertex AI Agent Engine.

Usage:
  python deploy_vertex_agent_engine.py  # reads GCP env-vars and deploys.

Prerequisites:
  pip install google-cloud-aiplatform[adk,agent_engines]
  • Ensure the following env-vars are set (or edit defaults below):
      GOOGLE_CLOUD_PROJECT, GOOGLE_CLOUD_LOCATION, STAGING_BUCKET

The script keeps costs low by:
  • Leaving min replica at zero (scale-to-zero) so you only pay for
    active traffic.
  • Using Vertex free-tier region us-central1.
"""

import os
from vertexai import agent_engines
from vertexai.preview import reasoning_engines
import vertexai

from multi_tool_agent.agent import root_agent

# ── Config (override via env) ────────────────────────────────────────────────
PROJECT_ID = os.getenv("GOOGLE_CLOUD_PROJECT", "multi-agent-462523")
LOCATION = os.getenv("GOOGLE_CLOUD_LOCATION", "us-central1")
STAGING_BUCKET = os.getenv("STAGING_BUCKET", f"gs://{PROJECT_ID}-agent-engine-staging")

print("🚀 Initialising Vertex AI SDK …")
vertexai.init(project=PROJECT_ID, location=LOCATION, staging_bucket=STAGING_BUCKET)

print("📦 Wrapping root_agent for Agent Engine …")
app = reasoning_engines.AdkApp(agent=root_agent, enable_tracing=False)

print("⏳ Deploying (this takes a few minutes the first time)…")
remote_app = agent_engines.create(
    agent_engine=app,
    requirements=[
        "google-cloud-aiplatform[adk,agent_engines]",
    ],
    # Autoscale down to zero to stay within free-tier when idle
    min_replica_count=0,
    max_replica_count=1,
)

print("✅ Deployed! Resource name:")
print(remote_app.resource_name) 