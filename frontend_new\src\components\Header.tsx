import { <PERSON>u, Wifi, WifiOff, Loader, User, LogOut } from 'lucide-react';
import { useAppStore } from '../stores/appStore';
import { useState } from 'react';

interface HeaderProps {
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
}

const Header = ({ connectionStatus }: HeaderProps) => {
  const { toggleSidebar, sessionId, auth, signOut } = useAppStore();
  const [showUserMenu, setShowUserMenu] = useState(false);

  const getConnectionIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <Wifi className="w-4 h-4" />;
      case 'connecting':
        return <Loader className="w-4 h-4 animate-spin" />;
      case 'error':
      case 'disconnected':
        return <WifiOff className="w-4 h-4" />;
    }
  };

  const getConnectionText = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'Connected to ADK';
      case 'connecting':
        return 'Connecting...';
      case 'error':
        return 'Connection Error';
      case 'disconnected':
        return 'Disconnected';
    }
  };

  const getConnectionClass = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'connection-connected';
      case 'connecting':
        return 'connection-connecting';
      case 'error':
        return 'connection-error';
      case 'disconnected':
        return 'connection-disconnected';
    }
  };

  const handleSignOut = () => {
    signOut();
    setShowUserMenu(false);
  };

  return (
    <header className="bg-white border-b border-gray-200 px-4 py-3">
      <div className="flex items-center justify-between">
        {/* Left side - Menu button and title */}
        <div className="flex items-center gap-3">
          <button
            onClick={toggleSidebar}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            aria-label="Toggle sidebar"
          >
            <Menu className="w-5 h-5 text-gray-600" />
          </button>
          
          <div>
            <h1 className="text-lg font-semibold text-gray-900">
              BC Parent Activity Assistant
            </h1>
            <p className="text-xs text-gray-500">
              Powered by Google ADK
            </p>
          </div>
        </div>

        {/* Right side - Connection status, session info, and user menu */}
        <div className="flex items-center gap-4">
          {/* Connection Status */}
          <div className={`connection-indicator ${getConnectionClass()}`}>
            {getConnectionIcon()}
            <span>{getConnectionText()}</span>
          </div>
          
          {/* Session ID (for debugging) */}
          {sessionId && (
            <div className="text-xs text-gray-400 font-mono">
              Session: {sessionId.slice(-8)}
            </div>
          )}

          {/* User Profile Menu */}
          {auth.user && (
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center gap-2 p-2 hover:bg-gray-100 rounded-lg transition-colors"
                aria-label="User menu"
              >
                {auth.user.picture ? (
                  <img
                    src={auth.user.picture}
                    alt={auth.user.name}
                    className="w-8 h-8 rounded-full"
                  />
                ) : (
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <User className="w-4 h-4 text-blue-600" />
                  </div>
                )}
                <span className="text-sm font-medium text-gray-700 max-w-24 truncate">
                  {auth.user.given_name || auth.user.name}
                </span>
              </button>

              {/* User Menu Dropdown */}
              {showUserMenu && (
                <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                  <div className="p-4 border-b border-gray-100">
                    <div className="flex items-center gap-3">
                      {auth.user.picture ? (
                        <img
                          src={auth.user.picture}
                          alt={auth.user.name}
                          className="w-10 h-10 rounded-full"
                        />
                      ) : (
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <User className="w-5 h-5 text-blue-600" />
                        </div>
                      )}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {auth.user.name}
                        </p>
                        <p className="text-xs text-gray-500 truncate">
                          {auth.user.email}
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-2">
                    <button
                      onClick={handleSignOut}
                      className="w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                    >
                      <LogOut className="w-4 h-4" />
                      Sign Out
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      
      {/* Click outside to close menu */}
      {showUserMenu && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowUserMenu(false)}
        />
      )}
    </header>
  );
};

export default Header; 