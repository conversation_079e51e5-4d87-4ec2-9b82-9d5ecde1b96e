import re
from datetime import datetime
from typing import Dict, Any, List, Optional

def _parse_time_range(time_range_str: Optional[str]) -> (Optional[str], Optional[str]):
    """Parses a time range string like '9:00 AM - 9:25 AM' into ISO start and end times."""
    if not time_range_str or '-' not in time_range_str:
        return None, None
    
    try:
        start_str, end_str = [t.strip() for t in time_range_str.split('-')]
        start_iso = datetime.strptime(start_str, "%I:%M %p").strftime("%H:%M:%S")
        end_iso = datetime.strptime(end_str, "%I:%M %p").strftime("%H:%M:%S")
        return start_iso, end_iso
    except (ValueError, IndexError):
        return None, None

def _format_time(time_iso: Optional[str]) -> str:
    """Converts 'HH:MM:SS' to a friendly 'H:MM AM/PM' format, handling None."""
    if not time_iso:
        return ""
    try:
        t = datetime.strptime(time_iso, "%H:%M:%S")
        # Use '%-I' on Linux/macOS, '%#I' on Windows to remove leading zero.
        # A simple string replace is more portable.
        formatted_time = t.strftime("%I:%M %p")
        if formatted_time.startswith('0'):
            return formatted_time[1:]
        return formatted_time
    except (ValueError, TypeError):
        return ""

def _parse_date(date_val):
    if not date_val or not isinstance(date_val, str): return None
    for fmt in ("%d-%b-%Y", "%Y-%m-%d", "%d/%m/%Y", "%m/%d/%Y", "%d-%m-%Y"):
        try: return datetime.strptime(date_val, fmt).strftime("%Y-%m-%d")
        except (ValueError, TypeError): pass
    return None

def _create_days_list(days_raw: str) -> List[str]:
    if not days_raw or not isinstance(days_raw, str): return []
    weekday_map = {"mon": "monday", "tue": "tuesday", "wed": "wednesday", "thu": "thursday", "fri": "friday", "sat": "saturday", "sun": "sunday"}
    day_tokens = {t[:3] for t in re.findall(r"[A-Za-z]{3,}", days_raw.lower())}
    return sorted(list({weekday_map[t] for t in day_tokens if t in weekday_map}))

def normalize_activity(activity: Dict[str, Any]) -> Dict[str, Any]:
    """
    Takes a raw activity from ANY scraper and returns a single, standardized,
    clean, and ENRICHED dictionary for Qdrant.
    """
    
    # --- Step 1: Extract and Normalize Core Fields from different possible keys ---
    name = activity.get("name")
    
    # Handle time differences
    start_time_iso = activity.get("start_time_iso")
    end_time_iso = activity.get("end_time_iso")
    if not start_time_iso and activity.get("start_time"): # Check for New West format
        start_time_iso, end_time_iso = _parse_time_range(activity.get("start_time"))
        
    # Handle description differences
    description = activity.get("description")
    if not description and activity.get("raw_api_data", {}):
        description = activity["raw_api_data"].get("desc")

    # --- Step 2: Create Pre-Formatted Display Strings ---
    min_age = activity.get("min_age_years")
    max_age = activity.get("max_age_years")
    display_age = f"Ages {min_age}-{max_age}"
    if min_age is None or max_age is None:
        display_age = "All Ages"
    elif min_age == max_age:
        display_age = f"Age {min_age}"

    start_time_str = _format_time(start_time_iso)
    end_time_str = _format_time(end_time_iso)
    display_time = f"{start_time_str} - {end_time_str}" if start_time_str and end_time_str else ""
    
    # --- Step 3: Build the Final, Unified Payload ---
    normalized = {
        "record_id": activity.get("record_id"),
        "name": name,
        "description": description, # Include the rich description for embedding
        "activity_url": activity.get("activity_url"),
        "category": activity.get("category"),
        "min_age_years": min_age,
        "max_age_years": max_age,
        "start_time_iso": start_time_iso,
        "end_time_iso": end_time_iso,
        "days_of_week_list": activity.get("days_of_week_list") or _create_days_list(activity.get("days_of_week")),
        "price_numeric": activity.get("price_numeric"),
        "is_open": activity.get("registration_status", "").lower() in ["open", "in progress"],
        "start_date": _parse_date(activity.get("start_date")),
        "end_date": _parse_date(activity.get("end_date")),
        "facility": activity.get("facility"),
        "source": activity.get("source"),
        
        # --- PRE-FORMATTED FIELDS FOR THE AGENT ---
        "display_age": display_age,
        "display_time": display_time,
    }

    # Infer city based on source
    source = activity.get("source", "").lower()
    if "new west" in source:
        normalized["city"] = "New Westminster"
    elif "burnaby" in source:
        normalized["city"] = "Burnaby"
    else:
        normalized["city"] = "Unknown"
        
    return normalized
