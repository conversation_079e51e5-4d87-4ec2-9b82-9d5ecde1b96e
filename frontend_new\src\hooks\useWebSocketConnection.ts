import { useEffect, useRef, useCallback } from 'react';
import { API_BASE_URL } from '../config';
import { useAppStore, createMessage, createError } from '../stores/appStore';
import type { Activity } from '../types/adk';

export const useWebSocketConnection = () => {
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectDelayRef = useRef(2000);

  const {
    sessionId,
    setConnectionStatus,
    addMessage,
    setIsAgentTyping,
    addError,
    addActivity,
    showConfirmationDialog,
  } = useAppStore();

  // helpers
  const connect = useCallback(() => {
    if (!sessionId) return;

    const wsUrl = `${API_BASE_URL.replace(/^http/, 'ws')}/ws`;
    console.log('🔗 WS URL:', wsUrl);

    const ws = new WebSocket(wsUrl);
    wsRef.current = ws;
    setConnectionStatus('connecting');

    ws.onopen = () => {
      console.log('🟢 WS open');
      reconnectDelayRef.current = 2000; // reset back-off
      setConnectionStatus('connected');
    };

    ws.onclose = () => {
      console.warn('🔌 WS closed');
      setConnectionStatus('disconnected');
      // auto-reconnect
      setTimeout(connect, reconnectDelayRef.current);
      reconnectDelayRef.current = Math.min(reconnectDelayRef.current * 1.5, 30000);
    };

    ws.onerror = (e) => {
      console.error('WS error', e);
      ws.close();
    };

    ws.onmessage = (e) => {
      try {
        const msg = JSON.parse(e.data);
        switch (msg.type) {
          case 'text_chunk':
            // simple stream – append or create message
            addMessage(createMessage('assistant', msg.content));
            break;
          case 'confirmation_request':
            // Show confirmation dialog
            showConfirmationDialog(msg.plan, msg.confirmation_id);
            break;
          case 'activity_card':
            (msg.activities as Activity[]).forEach((a) => addActivity(a));
            break;
          case 'turn_complete':
            setIsAgentTyping(false);
            break;
          case 'ping':
            // ignore
            break;
          default:
            console.log('WS event', msg);
        }
      } catch (err) {
        console.error('WS parse error', err);
      }
    };
  }, [sessionId, setConnectionStatus, addMessage, setIsAgentTyping, addActivity]);

  const disconnect = () => {
    wsRef.current?.close();
    wsRef.current = null;
  };

  const sendMessage = async (text: string) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      addError(createError('WebSocket not connected', 'connection'));
      return false;
    }
    const payload = {
      session_id: sessionId,
      content: text,
    };
    wsRef.current.send(JSON.stringify(payload));
    addMessage(createMessage('user', text));
    setIsAgentTyping(true);
    return true;
  };

  const sendConfirmationResponse = async (confirmationId: string, decision: 'yes' | 'no') => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      addError(createError('WebSocket not connected', 'connection'));
      return false;
    }
    const payload = {
      session_id: sessionId,
      type: 'confirmation_response',
      confirmation_id: confirmationId,
      decision: decision,
    };
    wsRef.current.send(JSON.stringify(payload));
    return true;
  };

  useEffect(() => {
    connect();
    return disconnect;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sessionId]);

  return {
    sendMessage,
    sendConfirmationResponse,
    connect,
    disconnect,
    isConnected: useAppStore(state => state.isConnected),
    connectionStatus: useAppStore(state => state.connectionStatus),
  };
}; 