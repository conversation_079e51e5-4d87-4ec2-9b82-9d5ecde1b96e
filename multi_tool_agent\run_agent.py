import asyncio
import logging
import sys
import os

# Add the parent directory to the path to fix imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the agent
from multi_tool_agent.orchestrator import root_agent

from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.adk.agents.run_config import RunConfig, StreamingMode  # Add streaming config imports
from google.genai import types # For creating message Content/Parts

# Configure basic logging to see ADK's internal INFO messages if needed,
# or set to ERROR for less verbosity.
logging.basicConfig(level=logging.INFO) # Changed to INFO to see streaming logs


async def call_agent(query: str, runner: Runner, user_id: str, session_id: str):
    """Sends a query to the agent and handles both streaming and direct tool responses."""
    print(f"\n>>> User Query: {query}")
    content = types.Content(role='user', parts=[types.Part(text=query)])
    
    final_response_text = "Agent did not produce a final response."
    
    # Enable streaming to see intermediate "thinking" steps if any
    run_config = RunConfig(streaming_mode=StreamingMode.SSE)
    
    async for event in runner.run_async(
        user_id=user_id, 
        session_id=session_id, 
        new_message=content, 
        run_config=run_config
    ):
        # Log events for debugging
        if event.content and event.content.parts and event.content.parts[0].text:
             print(f"  [Event] Author: {event.author}, Type: {type(event).__name__}, "
                   f"Final: {event.is_final_response()}, Text: '{event.content.parts[0].text[:80]}...'")

        if event.is_final_response():
            # HIGHEST PRIORITY: Check for a direct tool response
            if event.actions.skip_summarization and event.get_function_responses():
                # The response is the raw output from our tool
                final_response_text = event.get_function_responses()[0].response
            # Fallback for standard LLM-generated responses
            elif event.content and event.content.parts and event.content.parts[0].text:
                final_response_text = event.content.parts[0].text.strip()
            # Handle escalation/error
            elif event.actions and event.actions.escalate:
                final_response_text = f"Agent escalated: {event.error_message or 'No specific message.'}"
            
            break # We have the final answer

    print(f"<<< Agent Response:\n{final_response_text}")


async def main():
    """Sets up and runs the agent conversation with streaming test."""
    # --- Session Management ---
    session_service = InMemorySessionService()

    APP_NAME, USER_ID, SESSION_ID = "activity_finder_app", "test_user_01", "session_01"

    await session_service.create_session(
        app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID, state={}
    )
    print(f"Session created: App='{APP_NAME}', User='{USER_ID}', Session='{SESSION_ID}'")

    # --- Runner ---
    runner = Runner(agent=root_agent, app_name=APP_NAME, session_service=session_service)
    print(f"Runner created for agent '{runner.agent.name}' using model '{runner.agent.model}'.")

    # --- Test streaming with different queries ---
    test_queries = [
        "Find a gymnastics class for a 9-year-old on Thursdays in New Westminster.",
        "What art classes are available in Burnaby for a 7 year old that cost less than $200?",
        "I need an activity for my 11 year old on 2025-07-20.",
        "Are there any open spots for a trampoline class?",
        "what time is it in vancouver" # Test fallback to sub-agent
    ]
    
    for query in test_queries:
        await call_agent(query, runner, USER_ID, SESSION_ID)
        print("-" * 80)


if __name__ == "__main__":
    asyncio.run(main())