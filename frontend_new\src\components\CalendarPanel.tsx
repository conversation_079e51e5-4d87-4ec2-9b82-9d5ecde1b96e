import React, { useState, useEffect } from 'react';
import { Calendar, Download, AlertTriangle, Clock } from 'lucide-react';
import { useAppStore } from '../stores/appStore';
import { API_BASE_URL } from '../config';

interface Deadline {
  title: string;
  provider: string;
  deadline: string;
  description: string;
  urgency: 'low' | 'medium' | 'high';
}

export const CalendarPanel: React.FC = () => {
  const { sessionId } = useAppStore();
  const [deadlines, setDeadlines] = useState<Deadline[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDeadlines = async () => {
    if (!sessionId) return;
    setIsLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/calendar/upcoming-deadlines?session_id=${sessionId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch deadlines');
      }
      
      const data = await response.json();
      setDeadlines(data.deadlines || []);
    } catch (err) {
      console.error('Failed to fetch deadlines:', err);
      setError(err instanceof Error ? err.message : 'Failed to load deadlines');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDeadlines();
  }, [sessionId]);

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-amber-600 bg-amber-50 border-amber-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'high': return <AlertTriangle className="w-4 h-4" />;
      case 'medium': return <Clock className="w-4 h-4" />;
      default: return <Calendar className="w-4 h-4" />;
    }
  };

  const formatDeadline = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    const formattedDate = date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
    
    if (diffDays <= 0) {
      return `${formattedDate} (Today!)`;
    } else if (diffDays === 1) {
      return `${formattedDate} (Tomorrow)`;
    } else if (diffDays <= 7) {
      return `${formattedDate} (${diffDays} days)`;
    } else {
      return formattedDate;
    }
  };

  const exportToCalendar = async (deadline: Deadline) => {
    if (!sessionId) return;
    try {
      const response = await fetch(`${API_BASE_URL}/api/activity/export-calendar`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: deadline.title,
          provider: deadline.provider,
          registration_date: deadline.deadline,
          notes: deadline.description,
          location: 'British Columbia'
        }),
      });

      if (!response.ok) {
        throw new Error('Export failed');
      }

      // Create download link
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${deadline.title.replace(/[^a-zA-Z0-9]/g, '_')}.ics`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Failed to export calendar:', err);
      alert('Failed to export to calendar. Please try again.');
    }
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-4">
        <div className="flex items-center gap-2 mb-4">
          <Calendar className="w-5 h-5 text-blue-600" />
          <h3 className="font-medium text-gray-900">Upcoming Deadlines</h3>
        </div>

        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
            <p className="text-sm text-red-600">Failed to load deadlines: {error}</p>
            <button
              onClick={fetchDeadlines}
              className="text-red-700 text-sm underline mt-1"
            >
              Try again
            </button>
          </div>
        )}

        {!isLoading && !error && deadlines.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Calendar className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No upcoming deadlines</p>
          </div>
        )}

        {!isLoading && !error && deadlines.length > 0 && (
          <div className="space-y-3">
            {deadlines.map((deadline, index) => (
              <div
                key={index}
                className={`border rounded-lg p-3 ${getUrgencyColor(deadline.urgency)}`}
              >
                <div className="flex items-start justify-between gap-2">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      {getUrgencyIcon(deadline.urgency)}
                      <h4 className="font-medium text-sm truncate">
                        {deadline.title}
                      </h4>
                    </div>
                    
                    <p className="text-xs opacity-80 mb-1">
                      {deadline.provider}
                    </p>
                    
                    <p className="text-xs font-medium mb-2">
                      {formatDeadline(deadline.deadline)}
                    </p>
                    
                    <p className="text-xs opacity-75 leading-relaxed">
                      {deadline.description}
                    </p>
                  </div>
                  
                  <button
                    onClick={() => exportToCalendar(deadline)}
                    className="flex-shrink-0 p-1.5 rounded hover:bg-white/50 transition-colors"
                    title="Export to calendar"
                  >
                    <Download className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="mt-6 pt-4 border-t border-gray-200">
          <h4 className="font-medium text-sm text-gray-900 mb-3">
            Quick Tips
          </h4>
          <div className="space-y-2">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <p className="text-xs text-blue-700">
                💡 <strong>Pro Tip:</strong> Set up calendar reminders 1-2 weeks before deadlines to secure spots in popular programs.
              </p>
            </div>
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <p className="text-xs text-green-700">
                📅 <strong>Early Bird:</strong> Many BC programs offer discounts for early registration - sometimes 10-20% off!
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 