// ADK SSE Event Types
export interface ADKEvent {
  type: 'connection' | 'text_chunk' | 'tool_call_start' | 'tool_call_complete' | 
        'activity_card' | 'memory_update' | 'error' | 'turn_complete' | 'test' | 'heartbeat';
  data: any;
  timestamp: number;
  sessionId: string;
}

// Message Types
export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
  isComplete?: boolean;
}

// Tool Status
export interface ToolStatus {
  name: string;
  message: string;
  startTime: number;
  estimatedDuration?: string;
}

// Activity Data
export interface Activity {
  name: string;
  description: string;
  provider: string;
  location?: string;
  ageRange?: string;
  schedule?: string;
  registrationStatus?: 'open' | 'closing_soon' | 'full';
  price?: string;
  website?: string;
  extracted_from: string;
}

// Child Profile
export interface Child {
  name: string;
  age: number;
  grade?: string;
  interests: string[];
}

// Activity Preference
export interface Preference {
  childName: string;
  activityType: string;
  level: string;
  dateAdded: number;
}

// Registration Record
export interface Registration {
  id: string;
  childName: string;
  activityName: string;
  provider: string;
  status: string;
  year: number;
  session?: string;
}

// Memory Item
export interface MemoryItem {
  id: string;
  content: string;
  type: 'fact' | 'preference' | 'registration';
  timestamp: number;
}

// UI Error
export interface UIError {
  id: string;
  message: string;
  timestamp: number;
  type: 'connection' | 'session' | 'general';
}

// Session Status
export interface SessionStatus {
  status: 'active' | 'not_found' | 'error';
  session_id: string;
  adk_session_id?: string;
  created_at?: number;
  message_count?: number;
} 