import asyncio
import logging
from qdrant_client import AsyncQdrantClient, models
from config import AgentConfig

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def debug_qdrant():
    """Debug function to check Qdrant data for swimming preschool classes"""
    client = AsyncQdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
    
    try:
        print(f"\nConnecting to Qdrant at {AgentConfig.QDRANT_URL}")
        print(f"Collection: {AgentConfig.QDRANT_COLLECTION_NAME}\n")
        
        # 1. First, let's check for activities for age 4-6 (preschool age)
        print("=== SEARCHING FOR ACTIVITIES FOR AGES 4-6 ===")
        response = await client.scroll(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            scroll_filter=models.Filter(
                must=[
                    models.FieldCondition(key="min_age_years", range=models.Range(lte=6)),
                    models.FieldCondition(key="max_age_years", range=models.Range(gte=4))
                ]
            ),
            limit=20,
            with_payload=True
        )
        
        print(f"Found {len(response[0])} activities for ages 4-6:")
        swimming_count = 0
        for idx, point in enumerate(response[0]):
            payload = point.payload
            print(f"\n{idx + 1}. {payload.get('name')}")
            print(f"   City: {payload.get('city', 'NOT SET')}")
            print(f"   Facility: {payload.get('facility')}")
            print(f"   Age: {payload.get('min_age_years')}-{payload.get('max_age_years')}")
            print(f"   URL: {payload.get('activity_url', '')[:50]}...")
        
        # 2. Now let's check for New Westminster activities specifically
        print("\n\n=== SEARCHING FOR NEW WESTMINSTER ACTIVITIES (city field) ===")
        response2 = await client.scroll(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            scroll_filter=models.Filter(
                must=[models.FieldCondition(key="city", match=models.MatchValue(value="New Westminster"))]
            ),
            limit=3,
            with_payload=True
        )
        
        print(f"Found {len(response2[0])} activities with city='New Westminster':")
        for idx, point in enumerate(response2[0]):
            payload = point.payload
            print(f"\n{idx + 1}. {payload.get('name')}")
            print(f"   Category: {payload.get('category')}")
            print(f"   Facility: {payload.get('facility')}")
        
        # 3. Let's also check what's at təməsew̓txʷ facility
        print("\n\n=== SEARCHING BY FACILITY (təməsew̓txʷ) ===")
        response3 = await client.scroll(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            scroll_filter=models.Filter(
                must=[
                    models.FieldCondition(key="facility", match=models.MatchText(text="təməsew̓txʷ")),
                    models.FieldCondition(key="category", match=models.MatchText(text="Swimming"))
                ]
            ),
            limit=5,
            with_payload=True
        )
        
        print(f"Found {len(response3[0])} swimming activities at təməsew̓txʷ facility:")
        for idx, point in enumerate(response3[0]):
            payload = point.payload
            print(f"\n{idx + 1}. {payload.get('name')}")
            print(f"   City: {payload.get('city', 'NOT SET')}")
            print(f"   Age: {payload.get('min_age_years')}-{payload.get('max_age_years')}")
            
        # 4. Test our current filter logic
        print("\n\n=== TESTING COMBINED FILTER (age=5 + location=new west) ===")
        response4 = await client.scroll(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            scroll_filter=models.Filter(
                must=[
                    models.FieldCondition(key="min_age_years", range=models.Range(lte=5)),
                    models.FieldCondition(key="max_age_years", range=models.Range(gte=5)),
                    models.FieldCondition(
                        key="facility",
                        match=models.MatchText(text="təməsew̓txʷ Aquatic and Community Centre")
                    )
                ]
            ),
            limit=5,
            with_payload=True
        )
        
        print(f"Found {len(response4[0])} activities for 5-year-olds at New West facilities:")
        for idx, point in enumerate(response4[0]):
            payload = point.payload
            print(f"\n{idx + 1}. {payload.get('name')}")
            print(f"   Category: {payload.get('category')}")
            print(f"   Age range: {payload.get('min_age_years')}-{payload.get('max_age_years')}")
            
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(debug_qdrant())
