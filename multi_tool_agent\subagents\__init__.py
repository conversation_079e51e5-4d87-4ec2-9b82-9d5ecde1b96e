# Sub-agents package - Simplified for current implementation
from google.adk.agents import Agent
from ..config import AgentConfig

# Placeholder agents (not actually used in current implementation)
memory_agent = Agent(
    name="memory_agent",
    model=AgentConfig.ORCHESTRATOR_MODEL,
    description="Placeholder for memory functionality",
    instruction="This is a placeholder agent.",
    tools=[]
)

quick_reference_agent = Agent(
    name="quick_reference_agent", 
    model=AgentConfig.ORCHESTRATOR_MODEL,
    description="Placeholder for quick reference functionality",
    instruction="This is a placeholder agent.",
    tools=[]
)

news_research_agent = Agent(
    name="news_research_agent",
    model=AgentConfig.ORCHESTRATOR_MODEL, 
    description="Placeholder for news research functionality",
    instruction="This is a placeholder agent.",
    tools=[]
)

search_agent = Agent(
    name="search_agent",
    model=AgentConfig.ORCHESTRATOR_MODEL,
    description="Placeholder for search functionality", 
    instruction="This is a placeholder agent.",
    tools=[]
)

__all__ = [
    "memory_agent",
    "quick_reference_agent", 
    "news_research_agent",
    "search_agent"
]
