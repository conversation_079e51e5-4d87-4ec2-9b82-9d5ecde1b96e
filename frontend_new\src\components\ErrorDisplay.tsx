import { X, AlertCircle } from 'lucide-react';
import { useAppStore } from '../stores/appStore';

const ErrorDisplay = () => {
  const { errors, clearErrors } = useAppStore();

  if (errors.length === 0) return null;

  return (
    <div className="p-4 bg-red-50 border-b border-red-200">
      <div className="flex items-start gap-3">
        <AlertCircle className="w-5 h-5 text-red-500 mt-0.5" />
        <div className="flex-1">
          <h3 className="text-sm font-medium text-red-800">Connection Issues</h3>
          <div className="mt-1 space-y-1">
            {errors.map((error) => (
              <p key={error.id} className="text-sm text-red-700">
                {error.message}
              </p>
            ))}
          </div>
        </div>
        <button
          onClick={clearErrors}
          className="p-1 hover:bg-red-100 rounded transition-colors"
          aria-label="Dismiss errors"
        >
          <X className="w-4 h-4 text-red-500" />
        </button>
      </div>
    </div>
  );
};

export default ErrorDisplay; 