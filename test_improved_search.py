#!/usr/bin/env python3
"""
Test script to verify the improved search behavior.
Tests the search logic directly without agent framework.
"""

import asyncio
import sys
import os
sys.path.append('.')

# Set dummy API key to avoid errors
os.environ['GOOGLE_API_KEY'] = 'dummy'

from multi_tool_agent.tools.activity_search_tools import _perform_advanced_search, _create_qdrant_filter

async def test_search_improvements():
    """Test the improved search behavior"""
    print("=== TESTING IMPROVED SEARCH BEHAVIOR ===\n")

    print("=== TESTING IMPROVED SEARCH BEHAVIOR ===\n")

    # Test 1: General query - should return all age-appropriate options
    print("TEST 1: General query - 'swimming classes for 5 year olds in new west'")
    print("Expected: Should return ALL swimming options for 5-year-olds, not just beginner")

    filters1 = {'age': 5, 'location': 'new westminster'}
    qdrant_filter1 = _create_qdrant_filter(filters1)
    activities1 = await _perform_advanced_search('swimming classes', qdrant_filter1, limit=10)

    print(f"Found {len(activities1)} activities:")
    for i, activity in enumerate(activities1[:5]):
        name = activity.get('name', 'N/A')
        ages = f"{activity.get('min_age_years', '?')}-{activity.get('max_age_years', '?')}"
        print(f"  {i+1}. {name} (Ages {ages})")

    print("\n" + "="*60 + "\n")

    # Test 2: Explicit beginner query - should focus on beginner options
    print("TEST 2: Explicit beginner query - 'beginner swimming classes for 5 year olds'")
    print("Expected: Should prioritize beginner/level 1 options")

    filters2 = {'age': 5, 'location': 'new westminster', 'name_contains': 'beginner intro starter level 1'}
    qdrant_filter2 = _create_qdrant_filter(filters2)
    activities2 = await _perform_advanced_search('swimming classes beginner introduction starter', qdrant_filter2, limit=10)

    print(f"Found {len(activities2)} activities:")
    for i, activity in enumerate(activities2[:5]):
        name = activity.get('name', 'N/A')
        ages = f"{activity.get('min_age_years', '?')}-{activity.get('max_age_years', '?')}"
        print(f"  {i+1}. {name} (Ages {ages})")

    print("\n" + "="*60 + "\n")

    # Test 3: Check if name_contains filtering is working
    print("TEST 3: Name filtering - searching for 'level 02' specifically")

    filters3 = {'age': 5, 'location': 'new westminster', 'name_contains': 'level 02'}
    qdrant_filter3 = _create_qdrant_filter(filters3)
    activities3 = await _perform_advanced_search('swimming classes level 02', qdrant_filter3, limit=10)

    print(f"Found {len(activities3)} activities:")
    for i, activity in enumerate(activities3[:5]):
        name = activity.get('name', 'N/A')
        ages = f"{activity.get('min_age_years', '?')}-{activity.get('max_age_years', '?')}"
        print(f"  {i+1}. {name} (Ages {ages})")

if __name__ == "__main__":
    asyncio.run(test_search_improvements())
