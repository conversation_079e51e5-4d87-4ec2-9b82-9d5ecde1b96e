#!/usr/bin/env python3

import asyncio
import json
import os
import sys

from qdrant_client import AsyncQdrantClient, models
from qdrant_client.models import PointStruct, PointIdsList
from fastembed import TextEmbedding, SparseTextEmbedding, LateInteractionTextEmbedding

# Add the parent directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from config import AgentConfig
from multi_tool_agent.ingestion.data_utils import normalize_activity

# ... (format_and_enrich_for_qdrant function is correct) ...
def format_and_enrich_for_qdrant(activity: dict) -> dict:
    normalized_activity = normalize_activity(activity)
    text_parts = []
    if normalized_activity.get("name"): text_parts.append(normalized_activity['name'])
    if activity.get("description"): text_parts.append(activity['description'])
    if normalized_activity.get("category"): text_parts.append(normalized_activity['category'])
    if normalized_activity.get("facility"): text_parts.append(normalized_activity.get('facility'))
    if normalized_activity.get("general_location") and normalized_activity.get("general_location") != normalized_activity.get("facility"):
        text_parts.append(normalized_activity.get("general_location"))
    qdrant_record = {
        "id": activity["record_id"],
        "text_for_embedding": " | ".join(filter(None, text_parts)),
        "payload": normalized_activity,
    }
    qdrant_record['payload'] = {k: v for k, v in qdrant_record['payload'].items() if v is not None}
    return qdrant_record

async def ingest_activities_to_qdrant():
    """Ingest activities with dense, sparse, and colbert (multivector) embeddings."""
    
    print("=== Ingesting Activities (Dense + Sparse + ColBERT) ===")
    
    qdrant_client = AsyncQdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
    
    activities = []
    source_map = {
        os.path.join(os.path.dirname(__file__), 'ingestion', 'fast_dropin_activities.json'): 'New West PerfectMind',
        os.path.join(os.path.dirname(__file__), 'ingestion', 'burnaby_activities_complete.json'): 'Burnaby ActiveCommunities'
    }
    for file_path, source_name in source_map.items():
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                loaded_activities = json.load(f)
                for activity in loaded_activities:
                    activity['source'] = source_name
                activities.extend(loaded_activities)
    
    if not activities:
        print("Error: No activities loaded."); await qdrant_client.close(); return

    print("Preparing data and creating embeddings...")
    qdrant_records = [format_and_enrich_for_qdrant(activity) for activity in activities]
    texts_to_embed = [rec['text_for_embedding'] for rec in qdrant_records]
    
    # 1. Init models
    dense_embedder = TextEmbedding(model_name=AgentConfig.EMBEDDING_MODEL)
    sparse_embedder = SparseTextEmbedding(model_name=AgentConfig.SPARSE_EMBEDDING_MODEL)
    colbert_embedder = LateInteractionTextEmbedding(model_name=AgentConfig.COLBERT_MODEL)

    # 2. Generate all embeddings
    print("Generating dense embeddings...")
    dense_embeddings = list(dense_embedder.embed(texts_to_embed, batch_size=256))
    
    print("Generating sparse embeddings...")
    sparse_embeddings = list(sparse_embedder.embed(texts_to_embed, batch_size=256))
    
    print("Generating ColBERT multivector embeddings (this may take a while)...")
    colbert_embeddings = list(colbert_embedder.embed(texts_to_embed, batch_size=32)) # Smaller batch for ColBERT
    
    # 3. Create PointStruct objects with all three vectors
    points_to_upsert = []
    for i, rec in enumerate(qdrant_records):
        points_to_upsert.append(
            models.PointStruct(
                id=rec['id'], 
                vector={
                    "dense": dense_embeddings[i].tolist(),
                    "sparse": models.SparseVector(
                        indices=sparse_embeddings[i].indices.tolist(),
                        values=sparse_embeddings[i].values.tolist()
                    ),
                    "colbert": colbert_embeddings[i].tolist() # Multivector
                }, 
                payload=rec['payload']
            )
        )
    
    batch_size = 128
    print(f"Uploading {len(points_to_upsert)} points in batches of {batch_size}...")
    for i in range(0, len(points_to_upsert), batch_size):
        batch = points_to_upsert[i : i + batch_size]
        await qdrant_client.upsert(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            points=batch,
            wait=True
        )
    
    collection_info = await qdrant_client.get_collection(AgentConfig.QDRANT_COLLECTION_NAME)
    print(f"\nCollection updated successfully! Total points: {collection_info.points_count}")
    await qdrant_client.close()

if __name__ == "__main__":
    asyncio.run(ingest_activities_to_qdrant())
