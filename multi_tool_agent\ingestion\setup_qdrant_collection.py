# setup_qdrant_collection.py
import logging
import sys
import time
from pathlib import Path
from qdrant_client import QdrantClient, models

# Add the parent directory to the system path
sys.path.append(str(Path(__file__).parent.parent))
try:
    from config import AgentConfig
except ImportError as e:
    raise ImportError("Could not import AgentConfig. Ensure the config module is in the parent directory.") from e

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_collection(client: QdrantClient, collection_name: str):
    """
    Ensures the Qdrant collection exists with a production-ready HYBRID configuration.
    If it exists but is misconfigured, it is deleted and recreated.
    """
    recreate = False
    try:
        collection_info = client.get_collection(collection_name=collection_name)
        logger.info(f"Collection '{collection_name}' already exists. Verifying configuration...")

        # Check for correct dense and sparse vector configurations
        vectors_config = collection_info.config.params.vectors
        sparse_vectors_config = collection_info.config.params.sparse_vectors
        
        # Validate dense vector config
        if not isinstance(vectors_config, dict) or "dense" not in vectors_config:
            logger.warning("Dense vector 'dense' is not configured correctly.")
            recreate = True
        elif vectors_config["dense"].size != AgentConfig.EMBEDDING_DIM:
            logger.warning("Dense vector dimension mismatch!")
            recreate = True
            
        # Validate sparse vector config
        if not sparse_vectors_config or "sparse" not in sparse_vectors_config:
            logger.warning("Sparse vector 'sparse' is not configured.")
            recreate = True

        if recreate:
            logger.warning(f"Configuration is outdated. Deleting old collection '{collection_name}'...")
            client.delete_collection(collection_name=collection_name)
            time.sleep(1)
            create_new_collection(client, collection_name)
        else:
             logger.info("Collection schema appears up-to-date.")

    except Exception:
        logger.info(f"Collection '{collection_name}' not found. Creating it...")
        create_new_collection(client, collection_name)

    # --- Apply/Verify Payload Indexes (this is idempotent) ---
    logger.info("Applying payload indexes for efficient filtering...")
    
    # Keyword indexes for exact matching (city, days of week, etc.)
    keyword_fields = ["city", "facility", "category", "days_of_week_list", "source"]
    for field in keyword_fields:
        try:
            client.create_payload_index(collection_name=collection_name, field_name=field, field_schema=models.PayloadSchemaType.KEYWORD, wait=True)
            logger.info(f"  - Applied KEYWORD index on '{field}'")
        except Exception: pass # Index might already exist with same params

    # Text index for name field to enable name_contains filtering
    try:
        client.create_payload_index(collection_name=collection_name, field_name="name", field_schema=models.PayloadSchemaType.TEXT, wait=True)
        logger.info("  - Applied TEXT index on 'name'")
    except Exception: pass

    # Integer indexes for range filtering (age, price)
    numeric_fields = ["min_age_years", "max_age_years", "price_numeric"]
    for field in numeric_fields:
        try:
            client.create_payload_index(collection_name=collection_name, field_name=field, field_schema=models.PayloadSchemaType.FLOAT, wait=True)
            logger.info(f"  - Applied FLOAT index on '{field}'")
        except Exception: pass

    # Boolean index for open status
    try:
        client.create_payload_index(collection_name=collection_name, field_name="is_open", field_schema=models.PayloadSchemaType.BOOL, wait=True)
        logger.info("  - Applied BOOL index on 'is_open'")
    except Exception: pass

    # Date indexes
    date_fields = ["start_date", "end_date"]
    for field in date_fields:
        try:
            client.create_payload_index(collection_name=collection_name, field_name=field, field_schema=models.PayloadSchemaType.DATETIME, wait=True)
            logger.info(f"  - Applied DATETIME index on '{field}'")
        except Exception: pass

def create_new_collection(client: QdrantClient, collection_name: str):
    """Helper function to create a new collection with a 3-stage hybrid config + Binary Quantization."""
    quantization_status = "with Binary Quantization" if AgentConfig.ENABLE_BINARY_QUANTIZATION else "standard"
    logger.info(f"Creating new 3-stage HYBRID collection {quantization_status} '{collection_name}'...")

    # Configure dense vector parameters
    dense_vector_config = {
        "size": AgentConfig.EMBEDDING_DIM,
        "distance": models.Distance.COSINE,
        # Optimize HNSW for speed
        "hnsw_config": models.HnswConfigDiff(
            m=16,  # Lower m for faster search
            ef_construct=200,  # Higher ef_construct for better quality
            full_scan_threshold=10000  # Use full scan for small datasets
        )
    }

    # Add binary quantization if enabled
    if AgentConfig.ENABLE_BINARY_QUANTIZATION:
        dense_vector_config["quantization_config"] = models.BinaryQuantization(
            binary=models.BinaryQuantizationConfig(always_ram=True)
        )
        logger.info("🚀 Binary quantization enabled for ultra-fast retrieval")

    client.recreate_collection(
        collection_name=collection_name,
        vectors_config={
            "dense": models.VectorParams(**dense_vector_config),
            # Stage 2: ColBERT multivector config for reranking
            "colbert": models.VectorParams(
                size=AgentConfig.COLBERT_DIM,
                distance=models.Distance.COSINE,
                multivector_config=models.MultiVectorConfig(
                    comparator=models.MultiVectorComparator.MAX_SIM
                ),
                # CRITICAL: Disable HNSW indexing for the reranking vector
                hnsw_config=models.HnswConfigDiff(m=0, ef_construct=100)
            )
        },
        sparse_vectors_config={
            "sparse": models.SparseVectorParams(
                index=models.SparseIndexParams(on_disk=False)
            )
        },
        # Enable optimistic concurrency for faster writes
        optimizers_config=models.OptimizersConfigDiff(
            default_segment_number=2,
            max_segment_size=200000,
            memmap_threshold=200000,
            indexing_threshold=20000,
            flush_interval_sec=5,
            max_optimization_threads=2
        )
    )
    logger.info(f"3-stage hybrid collection {quantization_status} '{collection_name}' created successfully.")


if __name__ == "__main__":
    try:
        client = QdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
        collection_name = AgentConfig.QDRANT_COLLECTION_NAME
        
        logger.info(f"--- Running Qdrant Setup for Collection: '{collection_name}' ---")
        setup_collection(client, collection_name)
        logger.info("✅ Qdrant setup complete.")
    except Exception as e:
        logger.error(f"❌ Failed to complete Qdrant setup: {e}", exc_info=True)
