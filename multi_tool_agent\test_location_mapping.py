#!/usr/bin/env python3

import json
import os
from ingestion.fast_dropin_scraper import FastDropInScraper

async def test_location_mapping():
    """Test the improved location mapping logic"""
    
    print("🗺️ TESTING IMPROVED LOCATION MAPPING")
    print("="*50)
    
    # Load existing activities to test mapping
    json_path = os.path.join(os.path.dirname(__file__), 'ingestion', 'fast_dropin_activities.json')
    with open(json_path, 'r', encoding='utf-8') as f:
        activities = json.load(f)
    
    # Create a scraper instance to access the mapping methods
    scraper = FastDropInScraper()
    
    # Test activities with different location scenarios
    test_cases = []
    
    # Find activities with different location patterns
    for activity in activities[:100]:  # Test first 100
        general_location = activity.get('general_location', '')
        facility = activity.get('facility')
        location_id = activity.get('location_id')
        
        # Collect interesting test cases
        if not facility:  # Missing facility
            test_cases.append(activity)
        elif 'Skate Park' in general_location:  # Specific case mentioned
            test_cases.append(activity)
        elif 'Moody Park' in general_location:  # Another specific case
            test_cases.append(activity)
            
        if len(test_cases) >= 10:  # Limit to 10 test cases
            break
    
    print(f"Testing {len(test_cases)} activities with location mapping issues:")
    print("-" * 50)
    
    # Test BEFORE applying our improved mapping
    print("BEFORE improved mapping:")
    for i, activity in enumerate(test_cases):
        print(f"{i+1}. {activity.get('name', 'Unknown')}")
        print(f"   General Location: {activity.get('general_location', 'N/A')}")
        print(f"   Facility: {activity.get('facility', 'NOT SET')}")
        print(f"   Location ID: {activity.get('location_id', 'N/A')}")
        print()
    
    # Apply the improved location mapping
    print("AFTER improved mapping:")
    print("-" * 30)
    
    # We need to initialize the scraper's mappings first
    await scraper._bootstrap_filters()
    
    # Apply our improved mapping
    scraper._apply_location_mapping(test_cases)
    
    for i, activity in enumerate(test_cases):
        print(f"{i+1}. {activity.get('name', 'Unknown')}")
        print(f"   General Location: {activity.get('general_location', 'N/A')}")
        print(f"   Facility: {activity.get('facility', 'NOW SET' if activity.get('facility') else 'STILL NOT SET')}")
        print(f"   Location: {activity.get('location', 'N/A')}")
        print()
    
    # Count improvements
    facilities_set = sum(1 for act in test_cases if act.get('facility'))
    print(f"✅ Facilities successfully mapped: {facilities_set}/{len(test_cases)}")
    
    await scraper.aclose()

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_location_mapping())
