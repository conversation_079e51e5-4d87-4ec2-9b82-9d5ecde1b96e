import React, { useEffect, useRef, useState } from 'react';
import { useAppStore } from '../stores/appStore';

interface GoogleConfig {
  client_id: string;
  callback: (response: { credential: string }) => void;
}

interface ButtonConfig {
  theme: string;
  size: string;
  width?: string;
}

declare global {
  interface Window {
    google: {
      accounts: {
        id: {
          initialize: (config: GoogleConfig) => void;
          renderButton: (element: HTMLElement, config: ButtonConfig) => void;
        }
      }
    }
  }
}

const GoogleSignIn: React.FC = () => {
  const { signIn, auth } = useAppStore();
  const buttonRef = useRef<HTMLDivElement>(null);
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string>('');

  const clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;

  useEffect(() => {
    if (!clientId) {

      setError('Google Client ID not configured');
      return;
    }

    const script = document.createElement('script');
    script.src = 'https://accounts.google.com/gsi/client';
    script.onload = () => setIsReady(true);
    script.onerror = () => setError('Failed to load Google Sign-In');
    document.head.appendChild(script);

    return () => {
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, [clientId]);

  useEffect(() => {
    if (isReady && buttonRef.current && clientId && window.google) {
      window.google.accounts.id.initialize({
        client_id: clientId,
        callback: handleSignIn,
      });

      window.google.accounts.id.renderButton(buttonRef.current, {
        theme: 'outline',
        size: 'large',
      });
    }
  }, [isReady, clientId]);

  const handleSignIn = async (response: { credential: string }) => {
    if (!response.credential) {
      setError('No credential received');
      return;
    }

    try {
      setError('');
      await signIn(response.credential);
    } catch {
      setError('Sign in failed. Please try again.');
    }
  };

  if (!clientId) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
          <h1 className="text-xl font-bold mb-4">Configuration Required</h1>
          <p className="text-gray-600">
            Please set VITE_GOOGLE_CLIENT_ID environment variable.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Activity Assistant
          </h1>
          <p className="text-gray-600">
            Sign in to get personalized activity recommendations
          </p>
        </div>

        <div className="mb-6">
          {auth.isLoading ? (
            <div className="flex items-center justify-center py-3 px-4 bg-gray-100 rounded-lg">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
              <span>Signing in...</span>
            </div>
          ) : isReady ? (
            <div ref={buttonRef} className="w-full"></div>
          ) : (
            <div className="flex items-center justify-center py-3 px-4 bg-gray-100 rounded-lg">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-400 mr-3"></div>
              <span>Loading...</span>
            </div>
          )}
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <span className="text-red-700 text-sm">{error}</span>
          </div>
        )}

        <div className="text-xs text-gray-500 text-center">
          <p>Your data is stored securely and privately.</p>
        </div>
      </div>
    </div>
  );
};

export default GoogleSignIn;
