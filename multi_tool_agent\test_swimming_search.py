#!/usr/bin/env python3

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from multi_tool_agent.tools.activity_search_tools import search_activities

async def test_swimming_search():
    """Test if the agent can find Swimming Level 01 activities for a 5-year-old"""
    
    print("🏊 TESTING SWIMMING SEARCH FOR 5-YEAR-OLD")
    print("="*50)
    
    # Test 1: General swimming search for 5-year-old in New Westminster
    print("Test 1: Swimming for 5-year-old in New Westminster")
    print("-" * 40)
    
    try:
        results = await search_activities({
            "age": 5,
            "location": "New Westminster",
            "query": "swimming"
        })
        
        print(f"Found {len(results)} activities:")
        for i, activity in enumerate(results[:10]):  # Show first 10
            print(f"\n{i+1}. {activity.get('name')}")
            print(f"   Ages: {activity.get('min_age_years')}-{activity.get('max_age_years')}")
            print(f"   City: {activity.get('city')}")
            print(f"   Facility: {activity.get('facility')}")
            print(f"   Price: ${activity.get('price_numeric', 'N/A')}")
            print(f"   Category: {activity.get('category')}")
            
    except Exception as e:
        print(f"Error in Test 1: {e}")
    
    print("\n" + "="*50)
    
    # Test 2: Specific Swimming Level 01 search
    print("Test 2: Swimming Level 01 search")
    print("-" * 40)
    
    try:
        results = await search_activities({
            "age": 5,
            "location": "New Westminster", 
            "query": "Swimming Level 01"
        })
        
        print(f"Found {len(results)} Swimming Level 01 activities:")
        for i, activity in enumerate(results):
            print(f"\n{i+1}. {activity.get('name')}")
            print(f"   Ages: {activity.get('min_age_years')}-{activity.get('max_age_years')}")
            print(f"   City: {activity.get('city')}")
            print(f"   Start Date: {activity.get('start_date')}")
            print(f"   Days: {activity.get('days_of_week_list')}")
            print(f"   Time: {activity.get('display_time')}")
            print(f"   Price: ${activity.get('price_numeric', 'N/A')}")
            
    except Exception as e:
        print(f"Error in Test 2: {e}")
    
    print("\n" + "="*50)
    
    # Test 3: Preschool swimming specifically
    print("Test 3: Preschool swimming search")
    print("-" * 40)
    
    try:
        results = await search_activities({
            "age": 5,
            "location": "New Westminster",
            "query": "Swimming Level 01 Preschool"
        })
        
        print(f"Found {len(results)} preschool swimming activities:")
        for i, activity in enumerate(results):
            print(f"\n{i+1}. {activity.get('name')}")
            print(f"   Ages: {activity.get('min_age_years')}-{activity.get('max_age_years')}")
            print(f"   Start Date: {activity.get('start_date')}")
            print(f"   End Date: {activity.get('end_date')}")
            print(f"   Days: {activity.get('days_of_week_list')}")
            print(f"   Time: {activity.get('display_time')}")
            print(f"   Price: ${activity.get('price_numeric', 'N/A')}")
            print(f"   URL: {activity.get('activity_url', '')[:50]}...")
            
    except Exception as e:
        print(f"Error in Test 3: {e}")

if __name__ == "__main__":
    asyncio.run(test_swimming_search())
