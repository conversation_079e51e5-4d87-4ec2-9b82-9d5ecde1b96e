import { useEffect, useRef, useCallback } from 'react';
import type { ADKEvent, Activity } from '../types/adk';
import { useAppStore, createMessage, createError } from '../stores/appStore';
import { API_BASE_URL } from '../config';

export const useSSEConnection = () => {
  const eventSourceRef = useRef<EventSource | null>(null);
  
  // Refs for current message accumulation from SSE
  const sseCurrentMessageContentRef = useRef<string>('');
  const sseCurrentMessageIdRef = useRef<string | null>(null);
  
  const {
    sessionId,
    setConnectionStatus,
    addMessage,
    updateMessage,
    setCurrentTool,
    setIsAgentTyping,
    addActivity,
    addError,
    clearErrors
  } = useAppStore();

  // Memory refresh function
  const refreshMemory = useCallback(async () => {
    // Trigger memory refresh by dispatching a custom event
    // This allows the memory hook to listen for updates
    window.dispatchEvent(new CustomEvent('memoryRefresh', { 
      detail: { sessionId } 
    }));
  }, [sessionId]);

  const handleADKEvent = useCallback((event: ADKEvent) => {
    console.log('📨 SSE Event processed by handleADKEvent:', event.type, event.data);
    
    // Finalize any previous message if a new non-text event arrives
    if (event.type !== 'text_chunk' && sseCurrentMessageIdRef.current && sseCurrentMessageContentRef.current) {
      updateMessage(sseCurrentMessageIdRef.current, {
        content: sseCurrentMessageContentRef.current,
        isComplete: true,
      });
      sseCurrentMessageContentRef.current = '';
      sseCurrentMessageIdRef.current = null;
    }
    
    switch (event.type) {
      case 'connection':
        // Connection status is set in the onopen handler, no need to set here
        clearErrors();
        console.log('🔗 Connected to ADK backend:', event.data.message);
        break;
        
      case 'test':
        console.log('🧪 Test event from backend:', event.data.message);
        break;
        
      case 'text_chunk': {
        const { content, is_final: isFinal, full_response: fullResponse } = event.data;
        setIsAgentTyping(true);
        
        if (!sseCurrentMessageIdRef.current) {
          // Start of a new assistant message
          const newMessage = createMessage('assistant', content, false);
          sseCurrentMessageIdRef.current = newMessage.id;
          sseCurrentMessageContentRef.current = content;
          addMessage(newMessage);
        } else {
          // Append to existing assistant message
          sseCurrentMessageContentRef.current += content;
          updateMessage(sseCurrentMessageIdRef.current, {
            content: sseCurrentMessageContentRef.current,
            isComplete: false,
          });
        }
        
        if (isFinal) {
          // This is the final text_chunk for this turn's response
          if (sseCurrentMessageIdRef.current) {
            updateMessage(sseCurrentMessageIdRef.current, {
              content: fullResponse || sseCurrentMessageContentRef.current,
              isComplete: true,
            });
          }
          setIsAgentTyping(false);
          sseCurrentMessageContentRef.current = '';
          sseCurrentMessageIdRef.current = null;
        }
        break;
      }
        
      case 'tool_call_start': {
        // Finalize any text message being built
        if (sseCurrentMessageIdRef.current && sseCurrentMessageContentRef.current) {
          updateMessage(sseCurrentMessageIdRef.current, { 
            content: sseCurrentMessageContentRef.current, 
            isComplete: true 
          });
          sseCurrentMessageContentRef.current = '';
          sseCurrentMessageIdRef.current = null;
        }
        
        const { tool, message, estimated_duration } = event.data;
        setCurrentTool({
          name: tool,
          message,
          startTime: Date.now(),
          estimatedDuration: estimated_duration
        });
        setIsAgentTyping(true);
        break;
      }
        
      case 'tool_call_complete':
        setCurrentTool(null);
        // Keep typing indicator if text is expected to follow
        break;
        
      case 'activity_card': {
        const { activities } = event.data;
        if (Array.isArray(activities)) {
          activities.forEach((activity: Activity) => {
            addActivity(activity);
          });
        }
        break;
      }
        
      case 'turn_complete':
        setCurrentTool(null);
        setIsAgentTyping(false);
        
        // Finalize any pending message if not already done
        if (sseCurrentMessageIdRef.current && sseCurrentMessageContentRef.current) {
          updateMessage(sseCurrentMessageIdRef.current, {
            content: sseCurrentMessageContentRef.current,
            isComplete: true,
          });
        }
        // Always reset message accumulation refs on turn complete
        sseCurrentMessageContentRef.current = '';
        sseCurrentMessageIdRef.current = null;
        
        // Refresh memory after turn completion
        console.log('🧠 Refreshing memory after turn completion');
        refreshMemory();
        
        console.log('✅ Turn complete for session:', event.data.session_id);
        break;
        
      case 'error': {
        // Finalize any message being built before showing error
        if (sseCurrentMessageIdRef.current && sseCurrentMessageContentRef.current) {
          updateMessage(sseCurrentMessageIdRef.current, { 
            content: sseCurrentMessageContentRef.current, 
            isComplete: true 
          });
          sseCurrentMessageContentRef.current = '';
          sseCurrentMessageIdRef.current = null;
        }
        
        const error = createError(event.data.message, 'connection');
        addError(error);
        setConnectionStatus('error');
        setCurrentTool(null);
        setIsAgentTyping(false);
        console.error('❌ SSE Error:', event.data.message);
        break;
      }
        
      default:
        console.log('🔄 Unknown SSE event type:', event.type, event.data);
    }
  }, [setConnectionStatus, addMessage, updateMessage, setCurrentTool, setIsAgentTyping, addActivity, addError, clearErrors, refreshMemory]);

  const connect = useCallback(() => {
    if (!sessionId) {
      console.warn('⚠️ No session ID available for SSE connection');
      return;
    }
    
    if (eventSourceRef.current && eventSourceRef.current.readyState !== EventSource.CLOSED) {
      console.log('🔌 SSE connection already open or connecting.');
      return;
    }

    console.log('🚀 Attempting to establish SSE connection for session:', sessionId);
    setConnectionStatus('connecting');
    
    // Use the new connect endpoint
    const sseUrl = `${API_BASE_URL}/api/chat_stream_connect?session_id=${sessionId}`;
    console.log('🔗 SSE URL for EventSource:', sseUrl);
    
    const es = new EventSource(sseUrl);
    eventSourceRef.current = es;

    es.onopen = () => {
      console.log('🌊 SSE connection opened successfully.');
      setConnectionStatus('connected');
      clearErrors();
    };

    es.onmessage = (event) => {
      console.log('📨 Raw generic SSE message:', event.data);
      // Generic message handler for events without specific type
    };
    
    // Add listeners for specific event types from backend
    const eventTypes = ['connection', 'text_chunk', 'tool_call_start', 'tool_call_complete', 'activity_card', 'memory_update', 'error', 'turn_complete', 'heartbeat', 'test'];
    eventTypes.forEach(eventType => {
      es.addEventListener(eventType, (event) => {
        const messageEvent = event as MessageEvent;
        console.log(`📨 SSE Received typed event - Type: '${eventType}', Data:`, messageEvent.data);
        try {
          const parsedData = JSON.parse(messageEvent.data);
          handleADKEvent(parsedData);
        } catch (e) {
          console.error(`Error parsing SSE event type '${eventType}':`, e, messageEvent.data);
        }
      });
    });

    let retryDelay = 2000; // start with 2 s
    es.onerror = (errorEvent) => {
      console.error('❌ SSE connection error:', errorEvent, 'ReadyState:', es.readyState);
      setConnectionStatus('error');
      const connError = createError('SSE connection lost. Reconnecting…', 'connection');
      addError(connError);
      es.close();

      // Attempt auto-reconnect with exponential back-off (max 30 s)
      setTimeout(() => {
        // Increase delay for next time
        retryDelay = Math.min(retryDelay * 1.5, 30000);
        console.log(`🔄 Reconnecting SSE after ${retryDelay} ms…`);
        connect();
      }, retryDelay);
    };

  }, [sessionId, setConnectionStatus, handleADKEvent, addError, clearErrors]);

  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      console.log('🔌 SSE connection closed');
      eventSourceRef.current.close();
      eventSourceRef.current = null;
      setConnectionStatus('disconnected');
      
      // Reset message accumulation on disconnect
      sseCurrentMessageContentRef.current = '';
      sseCurrentMessageIdRef.current = null;
    }
  }, [setConnectionStatus]);

  const sendMessage = useCallback(async (content: string): Promise<boolean> => {
    if (!sessionId) {
      const error = createError('No active session', 'session');
      addError(error);
      return false;
    }
    
    if (!eventSourceRef.current || eventSourceRef.current.readyState !== EventSource.OPEN) {
      const error = createError('SSE connection not open. Please wait or reconnect.', 'connection');
      addError(error);
      console.warn('Attempted to send message, but SSE not open. State:', eventSourceRef.current?.readyState);
      return false;
    }

    try {
      // Add user message to chat immediately
      const userMessage = createMessage('user', content);
      addMessage(userMessage);
      
      // Reset client-side buffer for new assistant message
      sseCurrentMessageContentRef.current = '';
      sseCurrentMessageIdRef.current = null;
      setIsAgentTyping(true);

      // Send message to the new POST endpoint that triggers agent
      const response = await fetch(`${API_BASE_URL}/api/chat_message`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: sessionId,
          messages: [{ role: 'user', content }] // Simple message format
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: `HTTP error ${response.status}` }));
        throw new Error(errorData.message || `HTTP error ${response.status}`);
      }
      
      const ack = await response.json();
      console.log('📬 Message POST acknowledged by server:', ack);
      
      // Actual agent response will arrive via SSE connection
      return true;

    } catch (error) {
      console.error('❌ Error in sendMessage POST:', error);
      const sendError = createError(
        error instanceof Error ? error.message : 'Failed to send message',
        'general'
      );
      addError(sendError);
      setIsAgentTyping(false);
      return false;
    }
  }, [sessionId, addMessage, setIsAgentTyping, addError]);

  // Auto-connect when session ID changes or on mount
  useEffect(() => {
    if (sessionId) {
      connect();
    }
    
    // Cleanup function to close EventSource when component unmounts or sessionId changes
    return () => {
      if (eventSourceRef.current) {
        console.log('🧹 Cleaning up SSE connection for session:', sessionId);
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }
    };
  }, [sessionId, connect, disconnect]); // Added missing dependency

  return {
    connect,
    disconnect,
    sendMessage,
    isConnected: useAppStore(state => state.isConnected),
    connectionStatus: useAppStore(state => state.connectionStatus)
  };
};
