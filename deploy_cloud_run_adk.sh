#!/usr/bin/env bash
# Deploy the existing agent to Cloud Run with the ADK CLI.
# --------------------------------------------------------
# Prerequisites:
#   • pip install google-adk
#   • gcloud auth login && gcloud config set project <PROJECT>
#   • GOOGLE_API_KEY etc. already in .env or Dockerfile env.
#
# Usage:
#   chmod +x deploy_cloud_run_adk.sh
#   ./deploy_cloud_run_adk.sh
#
# The script keeps names / regions consistent with free-tier defaults.

# Convert path to Windows format if on Windows
convert_path() {
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        if command -v cygpath >/dev/null 2>&1; then
            cygpath -w "$1"
        else
            echo "$1" | sed -e 's/^\///' -e 's/\//\\/g' -e 's/^./\0:/'
        fi
    else
        echo "$1"
    fi
}

# Enable an API with retries
enable_api() {
    local api_name=$1
    echo "🔧 Enabling $api_name API..."
    if ! gcloud services enable "$api_name.googleapis.com" --project="$PROJECT"; then
        echo "❌ Failed to enable $api_name API"
        return 1
    fi
    
    # Wait for API to be active
    local attempts=0
    while ! gcloud services list --enabled --project="$PROJECT" | grep -q "$api_name.googleapis.com"; do
        sleep 5
        attempts=$((attempts+1))
        if [ $attempts -gt 12 ]; then
            echo "❌ Timed out waiting for $api_name API to enable"
            return 1
        fi
    done
    echo "✅ $api_name API enabled"
    return 0
}

set -euo pipefail
set -x

# ── Load secrets from .env if present ──────────────────────────────────────────
if [[ -f .env ]]; then
  # export every non-comment line as VAR=value
  echo "🔑 Loading secrets from .env …"
  # Strip Windows carriage returns to avoid sourcing errors on bash
  set -a; source <(grep -vE '^\s*#' .env | tr -d '\r'); set +a
fi

# ── Helper: add a var only if value is non-empty ──────────────────────────────
add_var() {
  local name="$1"; local val="$2"
  if [[ -n "$val" ]]; then
    ENV_VARS_STRING+="${ENV_VARS_STRING:+,}${name}=${val}"
  fi
}

# ── Required secrets check ───────────────────────────────────────────────────
REQUIRED=(NEO4J_URI NEO4J_USER NEO4J_PASSWORD GOOGLE_API_KEY QDRANT_URL QDRANT_API_KEY JWT_SECRET GOOGLE_CLIENT_ID)
for var in "${REQUIRED[@]}"; do
  if [[ -z "${!var:-}" ]]; then
    echo "❌ Required env var '$var' is not set. Put it in .env or your shell. Aborting." >&2
    exit 1
  fi
done

PROJECT=${GOOGLE_CLOUD_PROJECT:-multi-agent-462523}
REGION=${GOOGLE_CLOUD_LOCATION:-us-central1}
# AGENT_PATH should be the directory containing your agent's Python package (e.g., multi_tool_agent)
# Ensure this script is run from the parent directory of 'multi_tool_agent' (i.e., from 'agent_google')
AGENT_MODULE_NAME="multi_tool_agent" # The name of your agent's package/directory
AGENT_PATH=${AGENT_PATH:-./${AGENT_MODULE_NAME}} 
SERVICE_NAME=${SERVICE_NAME:-bc-activity-agent}
APP_NAME=${APP_NAME:-bc-activity-app}
# Temp folder for ADK build context (inspect on failure)
TEMP_FOLDER=${TEMP_FOLDER:-"$(pwd)/cloud_run_temp"}
TEMP_FOLDER=$(convert_path "$TEMP_FOLDER")
ADK_VERBOSITY=${ADK_VERBOSITY:-debug} # Set desired verbosity: debug, info, warning, error, critical

# copy directory with rsync if present, else fall back to cp -R
copy_dir() {
  local src="$1" dst="$2"
  if command -v rsync >/dev/null 2>&1; then
    rsync -a --delete --exclude='.git' --exclude='__pycache__' "$src/" "$dst/"
  else
    rm -rf "$dst"
    mkdir -p "$dst"
    if command -v robocopy >/dev/null 2>&1; then
      robocopy "$src" "$dst" /E /XD .git __pycache__ >nul || true
      return 0
    else
      cp -R "$src/" "$dst/"
      rm -rf "$dst/.git" "$dst/__pycache__"
    fi
  fi
}

if [[ -d graphiti ]]; then
  echo "📦 Copying local graphiti package into $AGENT_PATH …"
  if ! copy_dir "graphiti" "$AGENT_PATH/graphiti"; then
    echo "❌ Failed to copy graphiti directory. Aborting." >&2
    exit 2
  fi
  echo "✅ graphiti copy complete."
else
  echo "⚠️  graphiti directory not found at project root; make sure the package is installable."
fi

# Ensure requirements.txt is present inside AGENT_PATH (for ADK build)
if [[ -f requirements.txt ]]; then
  echo "📦 Copying root requirements.txt into $AGENT_PATH …"
  if ! cp requirements.txt "$AGENT_PATH/requirements.txt"; then
    echo "❌ Failed to copy requirements.txt. Aborting." >&2
    exit 3
  fi
  echo "✅ requirements.txt copy complete."
else
  echo "⚠️  requirements.txt not found at project root; ADK build may fail."
fi

# ── Build stamp (forces unique image + makes it visible at runtime) ────
BUILD_STAMP=${BUILD_STAMP:-$(date +%s)}
export BUILD_STAMP

# --- Define Runtime Environment Variables for the Agent ---
# For production, consider sourcing these from a secure location or using Google Secret Manager.
ENV_VARS_STRING=""
# Standard vars
add_var GOOGLE_GENAI_USE_VERTEXAI "true"
add_var GOOGLE_CLOUD_PROJECT       "$PROJECT"
add_var GOOGLE_CLOUD_LOCATION      "$REGION"
# PORT is excluded as it is a reserved variable set by Cloud Run
# App-specific secrets
add_var NEO4J_URI        "$NEO4J_URI"
add_var NEO4J_USER       "$NEO4J_USER"
add_var NEO4J_PASSWORD   "$NEO4J_PASSWORD"
add_var GOOGLE_API_KEY   "$GOOGLE_API_KEY"
add_var QDRANT_URL       "$QDRANT_URL"
add_var QDRANT_API_KEY   "$QDRANT_API_KEY"
add_var JWT_SECRET       "$JWT_SECRET"
add_var GOOGLE_CLIENT_ID "$GOOGLE_CLIENT_ID"
add_var GRAPHITI_MODEL "$GRAPHITI_MODEL"
add_var AGENT_MODEL "$AGENT_MODEL"
add_var BUILD_STAMP      "$BUILD_STAMP"

echo "🚀 Deploying $AGENT_PATH to Cloud Run ($PROJECT:$REGION) …"
echo "⚠️ This script will use the ADK CLI to build and deploy."
echo "   It will LIKELY NOT use your custom Dockerfile."
echo "   Ensure the 'graphiti' package is bundled within '$AGENT_PATH' or installable via its requirements."

# Check if ADK CLI is installed and accessible
echo "🔍 Checking ADK CLI installation..."
if ! command -v adk &> /dev/null; then
  echo "⚠️ ADK CLI not found. Attempting to install google-adk via pip..."
  if command -v python3 &> /dev/null; then
    # Ensure pip is available for python3
    if ! python3 -m pip --version &> /dev/null; then
      echo "🔧 pip not found for python3; bootstrapping via ensurepip…"
      python3 -m ensurepip --upgrade --default-pip || true
    fi
    if ! python3 -m pip --version &> /dev/null; then
      echo "🌐 ensurepip failed; downloading version-specific get-pip.py…"
      PY_VER=$(python3 - <<'PY'
import sys,platform,os
print(f"{sys.version_info.major}.{sys.version_info.minor}")
PY)
      if [[ "$PY_VER" == "" ]]; then PY_VER="3"; fi
      # Use legacy path for <3.9
      if [[ "$PY_VER" == "3.6" || "$PY_VER" == "3.7" || "$PY_VER" == "3.8" ]]; then
        GPP_URL="https://bootstrap.pypa.io/pip/${PY_VER}/get-pip.py"
      else
        GPP_URL="https://bootstrap.pypa.io/get-pip.py"
      fi
      echo "🔽 Downloading $GPP_URL …"
      curl -sSfL "$GPP_URL" | python3 - || true
    fi
    if python3 -m pip install --upgrade --quiet google-adk; then
      hash -r  # Refresh the shell's command cache so that newly installed 'adk' is found
      echo "✅ google-adk installed successfully."
      # Add user-base bin directory to PATH if needed (pip may install there)
      USER_BASE=$(python3 - <<'PY'
import site,sys; print(site.getuserbase())
PY)
      if [[ -d "$USER_BASE/bin" && ":$PATH:" != *":$USER_BASE/bin:"* ]]; then
        export PATH="$USER_BASE/bin:$PATH"
      fi
      # Verify adk is now on PATH
      if ! command -v adk &> /dev/null; then
        echo "⚠️ 'adk' still not found in PATH after installation. You may need to add $USER_BASE/bin to your PATH manually." >&2
      fi
    else
      echo "❌ Failed to install google-adk. Please install it manually (pip install google-adk) and re-run. Aborting." >&2
      exit 1
    fi
  elif command -v python &> /dev/null; then
    # Ensure pip is available for python
    if ! python -m pip --version &> /dev/null; then
      echo "🔧 pip not found for python; bootstrapping via ensurepip…"
      python -m ensurepip --upgrade --default-pip || true
    fi
    if ! python -m pip --version &> /dev/null; then
      echo "🌐 ensurepip failed; downloading version-specific get-pip.py…"
      PY_VER=$(python - <<'PY'
import sys,platform,os
print(f"{sys.version_info.major}.{sys.version_info.minor}")
PY)
      if [[ "$PY_VER" == "" ]]; then PY_VER="3"; fi
      if [[ "$PY_VER" == "3.6" || "$PY_VER" == "3.7" || "$PY_VER" == "3.8" ]]; then
        GPP_URL="https://bootstrap.pypa.io/pip/${PY_VER}/get-pip.py"
      else
        GPP_URL="https://bootstrap.pypa.io/get-pip.py"
      fi
      echo "🔽 Downloading $GPP_URL …"
      curl -sSfL "$GPP_URL" | python - || true
    fi
    if python -m pip install --upgrade --quiet google-adk; then
      hash -r
      echo "✅ google-adk installed successfully."
      # Add user-base bin directory to PATH if needed (pip may install there)
      USER_BASE=$(python - <<'PY'
import site,sys; print(site.getuserbase())
PY)
      if [[ -d "$USER_BASE/bin" && ":$PATH:" != *":$USER_BASE/bin:"* ]]; then
        export PATH="$USER_BASE/bin:$PATH"
      fi
      if ! command -v adk &> /dev/null; then
        echo "⚠️ 'adk' still not found in PATH after installation. You may need to add $USER_BASE/bin to your PATH manually." >&2
      fi
    else
      echo "❌ Failed to install google-adk. Please install it manually (pip install google-adk) and re-run. Aborting." >&2
      exit 1
    fi
  else
    echo "❌ Python interpreter not found. Cannot install google-adk automatically. Aborting." >&2
  exit 1
  fi
fi
echo "✅ ADK CLI found (or installed)."

# Ensure TEMP_FOLDER exists before deployment
echo "📁 Creating temporary folder at $TEMP_FOLDER..."
mkdir -p "$TEMP_FOLDER"

# Capture ADK CLI output to detect failures (ADK may return 0 even on failure)
set +e  # Allow command failure for graceful fallback
ADK_OUTPUT=$(adk deploy cloud_run \
  --project "$PROJECT" \
  --region  "$REGION" \
  --service_name "$SERVICE_NAME" \
  --app_name "$APP_NAME" \
  --temp_folder "$TEMP_FOLDER" \
  --port 8080 \
  --with_ui \
  "$AGENT_PATH" 2>&1)
ADK_EXIT_CODE=$?
set -e

# Preserve the contents of TEMP_FOLDER for debugging
echo "📁 Checking contents of $TEMP_FOLDER for debugging..."
if [[ -d "$TEMP_FOLDER" ]]; then
  echo "📋 Listing contents of $TEMP_FOLDER:"
  if command -v tree &> /dev/null; then
    # tree might be a broken Node shim on some systems; ignore errors and fallback
    tree "$TEMP_FOLDER" || ls -laR "$TEMP_FOLDER"
  else
    ls -laR "$TEMP_FOLDER"
  fi
else
  echo "⚠️ Temporary folder $TEMP_FOLDER does not exist."
fi

# Use exit code and output to decide fallback
if [[ $ADK_EXIT_CODE -ne 0 ]] || echo "$ADK_OUTPUT" | grep -qi "Deploy failed"; then
  echo "❌ ADK deploy failed. Falling back to gcloud builds & deploy..." >&2
  echo "$ADK_OUTPUT" >&2
  
  # Ensure Cloud Build API is enabled and wait for propagation
  if ! enable_api "cloudbuild"; then
    echo "❌ Failed to enable Cloud Build API. Aborting." >&2
    exit 4
  fi
  echo "⏳ Waiting for Cloud Build API to become fully operational…"
  for i in {1..12}; do
    if gcloud builds list --project="$PROJECT" &>/dev/null; then
      echo "✅ Cloud Build API is ready."
      break
    fi
    echo "   Still propagating… retry $i/12"
    sleep 10
  done
  # Small extra buffer
  sleep 5

  echo "🔄 Fallback: building container with gcloud builds submit using your Dockerfile…"
  
  # Add retry logic for API propagation
  build_attempts=0
  max_build_attempts=5
  build_success=false
  
  while [[ $build_attempts -lt $max_build_attempts && $build_success == false ]]; do
    if gcloud builds submit --project="$PROJECT" --tag="gcr.io/$PROJECT/$SERVICE_NAME" .; then
      build_success=true
    else
      echo "⚠️ Build attempt $((build_attempts+1)) failed. Retrying in 15 seconds..."
      sleep 15
      build_attempts=$((build_attempts+1))
    fi
  done

  if [[ $build_success == false ]]; then
    echo "❌ gcloud build failed after $max_build_attempts attempts. Aborting." >&2
    exit 5
  fi
  
  echo "🔄 Fallback: deploying container to Cloud Run..."
  if ! gcloud run deploy "$SERVICE_NAME" \
    --image "gcr.io/$PROJECT/$SERVICE_NAME" \
    --region "$REGION" \
    --platform managed \
    --allow-unauthenticated \
    --timeout 300 \
    --update-env-vars="$ENV_VARS_STRING"; then
    echo "❌ gcloud deploy failed. Aborting." >&2
    exit 6
  fi
  
  SERVICE_URL=$(gcloud run services describe "$SERVICE_NAME" --platform managed --region "$REGION" --project "$PROJECT" --format 'value(status.url)' 2>/dev/null)
  echo "✅ Fallback deployment succeeded. Service URL: $SERVICE_URL"
  exit 0
fi

# Print ADK output for visibility
echo "$ADK_OUTPUT"
echo "✅ ADK deployment completed."

echo "⏳ Waiting for Cloud Run service to appear …"
until gcloud run services describe "$SERVICE_NAME" --platform managed --region "$REGION" --project "$PROJECT" &>/dev/null; do
  sleep 3
done

echo "🔧 Configuring runtime environment variables for service: $SERVICE_NAME..."
gcloud run services update "$SERVICE_NAME" \
  --platform managed \
  --region "$REGION" \
  --project "$PROJECT" \
  --update-env-vars="$ENV_VARS_STRING"

echo "✅ Environment variables updated."
SERVICE_URL=$(gcloud run services describe "$SERVICE_NAME" --platform managed --region "$REGION" --project "$PROJECT" --format 'value(status.url)' 2>/dev/null || echo "Service URL not available yet.")
echo "🔗 Service URL: $SERVICE_URL"
echo "   (It may take a few minutes for the service to be fully ready and for the URL to reflect all changes)."
echo "🔑 If you were prompted and selected 'N' for unauthenticated invocations, you'll need to authenticate to access the UI/API."

# ── Verify gcloud authentication ─────────────────────────────────────────────
if ! command -v gcloud >/dev/null 2>&1; then
  echo "❌ Google Cloud SDK (gcloud) is not installed or not on PATH. Install it first: https://cloud.google.com/sdk/docs/install" >&2
  exit 1
fi

# Fail fast if no active account (avoids later API failures)
ACTIVE_ACCOUNT=$(gcloud config get-value account 2>/dev/null || echo "")
if [[ -z "$ACTIVE_ACCOUNT" || "$ACTIVE_ACCOUNT" == "None" ]]; then
  echo "❌ No active gcloud account. Run 'gcloud auth login' (same shell) then re-run this script." >&2
  exit 1
fi

# Ensure project is set for this gcloud config
GCLOUD_PROJECT=$(gcloud config get-value project 2>/dev/null || echo "")
if [[ -z "$GCLOUD_PROJECT" || "$GCLOUD_PROJECT" == "None" ]]; then
  echo "❌ No active gcloud project. Run 'gcloud config set project "$PROJECT"' then re-run this script." >&2
  exit 1
fi
