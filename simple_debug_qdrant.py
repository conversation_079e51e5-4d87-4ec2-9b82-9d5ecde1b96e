#!/usr/bin/env python3

import asyncio
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'multi_tool_agent'))
from qdrant_client import AsyncQdrantClient, models
from multi_tool_agent.config import AgentConfig

async def simple_debug():
    """Simple debug script to check Qdrant data without problematic filters"""
    
    client = AsyncQdrantClient(
        url=AgentConfig.QDRANT_URL,
        api_key=AgentConfig.QDRANT_API_KEY,
    )
    
    try:
        print(f"Connecting to Qdrant at {AgentConfig.QDRANT_URL}")
        print(f"Collection: {AgentConfig.QDRANT_COLLECTION_NAME}")
        
        # 1. Get total count
        print("\n=== TOTAL ACTIVITY COUNT ===")
        collection_info = await client.get_collection(AgentConfig.QDRANT_COLLECTION_NAME)
        print(f"Total activities in database: {collection_info.points_count}")
        
        # 2. Get a sample of activities to check data structure
        print("\n=== SAMPLE ACTIVITIES ===")
        response = await client.scroll(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            limit=50,
            with_payload=True
        )
        
        sources = {}
        cities = {}
        swimming_level_01_count = 0
        nw_activities = []
        
        for point in response[0]:
            payload = point.payload
            source = payload.get('source', 'Unknown')
            city = payload.get('city', 'Unknown')
            name = payload.get('name', '')
            
            sources[source] = sources.get(source, 0) + 1
            cities[city] = cities.get(city, 0) + 1
            
            if 'Swimming Level 01' in name:
                swimming_level_01_count += 1
                
            if city == 'New Westminster':
                nw_activities.append(point)
        
        print("Sources found in sample:")
        for source, count in sources.items():
            print(f"  {source}: {count}")
            
        print("\nCities found in sample:")
        for city, count in cities.items():
            print(f"  {city}: {count}")
            
        print(f"\nSwimming Level 01 activities in sample: {swimming_level_01_count}")
        
        # 3. Show New Westminster activities found
        print(f"\n=== NEW WESTMINSTER ACTIVITIES IN SAMPLE ({len(nw_activities)}) ===")
        for idx, point in enumerate(nw_activities):
            payload = point.payload
            print(f"\n{idx + 1}. {payload.get('name')}")
            print(f"   Category: {payload.get('category')}")
            print(f"   Facility: {payload.get('facility')}")
            print(f"   Source: {payload.get('source')}")
            print(f"   Age: {payload.get('age_info')}")
            print(f"   Record ID: {payload.get('record_id', 'N/A')[:8]}...")
        
        # 4. Search for ALL Swimming Level 01 activities
        print("\n=== COMPREHENSIVE SWIMMING LEVEL 01 SEARCH ===")
        try:
            all_swimming_01 = []
            offset = None

            while True:
                response2 = await client.scroll(
                    collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
                    scroll_filter=models.Filter(
                        must=[
                            models.FieldCondition(key="name", match=models.MatchText(text="Swimming Level 01"))
                        ]
                    ),
                    limit=100,
                    offset=offset,
                    with_payload=True
                )

                points, next_offset = response2
                all_swimming_01.extend(points)

                if next_offset is None:
                    break
                offset = next_offset

            print(f"Found {len(all_swimming_01)} Swimming Level 01 activities TOTAL")
            print(f"Expected: ~160 activities")
            print(f"Coverage: {len(all_swimming_01)/160*100:.1f}% of expected")

            # Analyze by subcategory
            subcategories = {}
            facilities = {}

            for point in all_swimming_01:
                payload = point.payload
                name = payload.get('name', '')
                facility = payload.get('facility', 'Unknown')

                if 'Preschool' in name:
                    subcategory = 'Preschool'
                elif 'Children' in name:
                    subcategory = 'Children'
                elif 'Tots' in name:
                    subcategory = 'Tots - Beginner'
                elif 'Adult and Youth' in name:
                    subcategory = 'Adult and Youth'
                elif 'Caregiver Participation' in name:
                    subcategory = 'Caregiver Participation'
                else:
                    subcategory = 'Other'

                subcategories[subcategory] = subcategories.get(subcategory, 0) + 1
                facilities[facility] = facilities.get(facility, 0) + 1

            print(f"\nBreakdown by subcategory:")
            for subcategory, count in sorted(subcategories.items()):
                print(f"  {subcategory}: {count} activities")

            print(f"\nBreakdown by facility:")
            for facility, count in sorted(facilities.items()):
                print(f"  {facility}: {count} activities")

        except Exception as e:
            print(f"Error searching for Swimming Level 01: {e}")
        
        # 5. Check if we can find any activities with record_id from JSON
        print("\n=== CHECKING FOR SPECIFIC RECORD IDs FROM JSON ===")
        # These are some record_ids from the JSON file we saw
        test_record_ids = [
            "106f285a-1449-5669-afce-01a3486071b5",  # Swimming Level 01 - Children
            "e6c9b4df-cc45-508e-bc65-d3c7b37143b9",  # Swimming Level 01 - Preschool
            "613daa77-9e0d-50e9-baf9-b9b141c35d9c"   # Yoga 50+
        ]
        
        for record_id in test_record_ids:
            try:
                response3 = await client.scroll(
                    collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
                    scroll_filter=models.Filter(
                        must=[
                            models.FieldCondition(key="record_id", match=models.MatchValue(value=record_id))
                        ]
                    ),
                    limit=1,
                    with_payload=True
                )
                
                if response3[0]:
                    payload = response3[0][0].payload
                    print(f"✓ Found record {record_id[:8]}...: {payload.get('name')}")
                else:
                    print(f"✗ Missing record {record_id[:8]}...: Expected from JSON")
                    
            except Exception as e:
                print(f"✗ Error checking record {record_id[:8]}...: {e}")
        
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(simple_debug())
