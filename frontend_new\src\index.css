@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom BC Activity Assistant Styles */
@layer base {
  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }
}

/* Custom Scrollbar Styles */
@layer utilities {
  /* Webkit browsers (Chrome, Safari, Edge) */
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
  
  /* Firefox */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
  }
}

@layer components {
  .activity-card {
    @apply bg-white rounded-lg border border-gray-200 p-4 shadow-sm hover:shadow-md transition-shadow;
  }
  
  .tool-status {
    @apply flex items-center gap-2 px-3 py-2 bg-blue-50 text-blue-700 rounded-lg text-sm;
  }
  
  .message-user {
    @apply bg-primary-500 text-white rounded-lg px-4 py-2 max-w-md ml-auto;
  }
  
  .message-assistant {
    @apply bg-gray-100 text-gray-900 rounded-lg px-4 py-2 max-w-md mr-auto;
  }
  
  .sidebar-panel {
    @apply p-4 border-b border-gray-200 last:border-b-0;
  }
  
  .connection-indicator {
    @apply inline-flex items-center gap-1 text-xs font-medium;
  }
  
  .connection-connected {
    @apply text-green-600;
  }
  
  .connection-connecting {
    @apply text-yellow-600;
  }
  
  .connection-error {
    @apply text-red-600;
  }
  
  .connection-disconnected {
    @apply text-gray-500;
  }
}

/* Activity type specific colors */
.activity-bike {
  @apply border-l-4 border-activity-bike;
}

.activity-swim {
  @apply border-l-4 border-activity-swim;
}

.activity-soccer {
  @apply border-l-4 border-activity-soccer;
}

.activity-camp {
  @apply border-l-4 border-activity-camp;
}

/* Animations */
@keyframes typing {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.typing-indicator {
  animation: typing 1.5s ease-in-out infinite;
}

.pulse-gentle {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
