# Qdrant Optimization Guide

This guide explains the three optimization scenarios for your Qdrant collection and how to configure them.

## 🚀 Quick Start

1. **Choose your scenario** based on your memory constraints:
   ```bash
   python configure_qdrant_scenario.py
   ```

2. **Re-ingest your data**:
   ```bash
   python update_qdrant_activities.py
   ```

3. **Test the configuration**:
   ```bash
   python test_qdrant_optimization.py
   ```

## 📊 Optimization Scenarios

### Scenario 1: High-Speed Search with Low Memory Usage
**Best for**: Production with limited RAM (4-8GB available)

**Configuration**:
- Original vectors: On disk (saves RAM)
- HNSW index: In RAM (fast search)
- Quantized vectors: In RAM (fast search)
- Precision: Good (slightly reduced due to quantization)

**Memory Usage**: ~50% less than Scenario 3

### Scenario 2: High Precision with Low Memory Usage  
**Best for**: Memory-constrained environments (<4GB available)

**Configuration**:
- Original vectors: On disk
- HNSW index: On disk
- Quantized vectors: On disk
- Precision: Best (full precision vectors)

**Memory Usage**: Minimal (disk-based)

### Scenario 3: High Precision with High-Speed Search ⭐
**Best for**: Production with sufficient RAM (>8GB available)

**Configuration**:
- Original vectors: In RAM (maximum speed)
- HNSW index: In RAM (fast search)
- Quantized vectors: In RAM (fast search)
- Precision: Best

**Memory Usage**: Highest (everything in RAM)

## 🔧 Manual Configuration

If you prefer to configure manually, edit `multi_tool_agent/ingestion/setup_qdrant_collection.py`:

```python
# In main() function, change this line:
optimization_scenario = "high_precision_high_speed"  # or other scenarios
```

## 📈 Performance Comparison

| Scenario | Search Speed | Memory Usage | Precision | Best For |
|----------|-------------|--------------|-----------|----------|
| 1 | Fast | Low | Good | Limited RAM |
| 2 | Slow | Minimal | Best | Very limited RAM |
| 3 | Fastest | High | Best | Sufficient RAM |

## 🐛 Troubleshooting

### HNSW Index Not Enabled
**Error**: `❌ HNSW index is disabled or misconfigured`

**Fix**: Ensure `m > 0` in your HNSW configuration:
```python
hnsw_config=models.HnswConfigDiff(
    payload_m=16,
    m=16,  # Must be > 0
    ef_construct=100
)
```

### Memory Issues
**Error**: Out of memory errors

**Solution**: Switch to Scenario 1 or 2:
```bash
python configure_qdrant_scenario.py
# Choose option 2 or 3
```

### Slow Search Performance
**Issue**: Search taking >1 second

**Solutions**:
1. Ensure HNSW is enabled (`m > 0`)
2. Use database-level filtering (already implemented)
3. Consider Scenario 3 if you have sufficient RAM

## 🔍 Verification Commands

Check your current configuration:
```bash
python test_qdrant_optimization.py
```

Expected output for Scenario 3:
```
✅ HNSW index is enabled with m=16
   HNSW on disk: False
Vectors on disk: False
Quantization: int8
Quantized vectors in RAM: True
Detected optimization scenario: High Precision & High Speed (Scenario 3)
```

## 📝 Migration Notes

- **Existing collections**: Will be recreated when you change scenarios
- **Data**: Must be re-ingested after changing scenarios
- **Indexes**: All payload indexes are preserved across scenarios

## 🎯 Recommendations

1. **Start with Scenario 3** if you have >8GB RAM available
2. **Use Scenario 1** if you experience memory issues
3. **Use Scenario 2** only if RAM is severely limited
4. **Monitor performance** with `test_qdrant_optimization.py`
5. **Profile memory usage** in production to optimize further 