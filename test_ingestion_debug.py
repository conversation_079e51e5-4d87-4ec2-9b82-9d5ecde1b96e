#!/usr/bin/env python3
"""
Debug script to test data ingestion with a small subset of activities.
This will help us identify why the full ingestion is failing.
"""

import asyncio
import json
import os
import sys
sys.path.append('.')

from qdrant_client import AsyncQdrantClient, models
from fastembed import TextEmbedding, SparseTextEmbedding, LateInteractionTextEmbedding
from multi_tool_agent.config import AgentConfig
from multi_tool_agent.ingestion.data_utils import normalize_activity

def format_and_enrich_for_qdrant(activity: dict) -> dict:
    """Format activity for Qdrant ingestion"""
    normalized_activity = normalize_activity(activity)
    text_parts = []
    if normalized_activity.get("name"): 
        text_parts.append(normalized_activity['name'])
    if activity.get("description"): 
        text_parts.append(activity['description'])
    if normalized_activity.get("category"): 
        text_parts.append(normalized_activity['category'])
    if normalized_activity.get("facility"): 
        text_parts.append(normalized_activity.get('facility'))
    
    qdrant_record = {
        "id": activity["record_id"],
        "text_for_embedding": " | ".join(filter(None, text_parts)),
        "payload": normalized_activity,
    }
    qdrant_record['payload'] = {k: v for k, v in qdrant_record['payload'].items() if v is not None}
    return qdrant_record

async def test_small_ingestion():
    """Test ingestion with just a few Swimming Level 01 activities"""
    
    print("🧪 TESTING SMALL INGESTION")
    print("="*50)
    
    # Load just a few Swimming Level 01 activities
    with open('multi_tool_agent/ingestion/fast_dropin_activities.json', 'r', encoding='utf-8') as f:
        all_activities = json.load(f)
    
    # Filter for Swimming Level 01 - Preschool (ages 4-6) activities
    swimming_01_activities = [
        act for act in all_activities 
        if 'Swimming Level 01 - Preschool' in act.get('name', '') and act.get('age_info') == '(4 - 6)'
    ][:5]  # Take just 5 for testing
    
    print(f"Selected {len(swimming_01_activities)} Swimming Level 01 - Preschool activities for testing")
    
    # Add source information
    for activity in swimming_01_activities:
        activity['source'] = 'New West PerfectMind'
    
    # Check current database state
    client = AsyncQdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
    
    try:
        collection_info = await client.get_collection(AgentConfig.QDRANT_COLLECTION_NAME)
        print(f"Current points in collection: {collection_info.points_count}")
        
        # Prepare data
        print("\n📋 Preparing activities for ingestion...")
        qdrant_records = [format_and_enrich_for_qdrant(activity) for activity in swimming_01_activities]
        
        for i, record in enumerate(qdrant_records):
            print(f"{i+1}. {record['payload'].get('name')} - {record['payload'].get('city')}")
            print(f"   Ages: {record['payload'].get('min_age_years')}-{record['payload'].get('max_age_years')}")
            print(f"   Price: ${record['payload'].get('price_numeric')}")
            print(f"   Text: {record['text_for_embedding'][:100]}...")
            print()
        
        # Test embedding generation (just dense for speed)
        print("🔄 Generating embeddings...")
        texts_to_embed = [rec['text_for_embedding'] for rec in qdrant_records]
        
        dense_embedder = TextEmbedding(model_name=AgentConfig.EMBEDDING_MODEL)
        sparse_embedder = SparseTextEmbedding(model_name=AgentConfig.SPARSE_EMBEDDING_MODEL)
        colbert_embedder = LateInteractionTextEmbedding(model_name=AgentConfig.COLBERT_MODEL)
        
        print("  - Dense embeddings...")
        dense_embeddings = list(dense_embedder.embed(texts_to_embed, batch_size=5))
        
        print("  - Sparse embeddings...")
        sparse_embeddings = list(sparse_embedder.embed(texts_to_embed, batch_size=5))
        
        print("  - ColBERT embeddings...")
        colbert_embeddings = list(colbert_embedder.embed(texts_to_embed, batch_size=2))
        
        # Create points
        print("📤 Creating Qdrant points...")
        points_to_upsert = []
        for i, rec in enumerate(qdrant_records):
            points_to_upsert.append(
                models.PointStruct(
                    id=rec['id'], 
                    vector={
                        "dense": dense_embeddings[i].tolist(),
                        "sparse": models.SparseVector(
                            indices=sparse_embeddings[i].indices.tolist(),
                            values=sparse_embeddings[i].values.tolist()
                        ),
                        "colbert": colbert_embeddings[i].tolist()
                    }, 
                    payload=rec['payload']
                )
            )
        
        # Upload to Qdrant
        print("⬆️  Uploading to Qdrant...")
        await client.upsert(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            points=points_to_upsert,
            wait=True
        )
        
        # Check final state
        final_collection_info = await client.get_collection(AgentConfig.QDRANT_COLLECTION_NAME)
        print(f"✅ Upload complete! Total points: {final_collection_info.points_count}")
        
        # Test search
        print("\n🔍 Testing search...")
        response = await client.scroll(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            scroll_filter=models.Filter(
                must=[
                    models.FieldCondition(key='city', match=models.MatchValue(value='New Westminster')),
                    models.FieldCondition(key='min_age_years', range=models.Range(lte=5)),
                    models.FieldCondition(key='max_age_years', range=models.Range(gte=5))
                ]
            ),
            limit=10,
            with_payload=True
        )
        
        print(f"Found {len(response[0])} New Westminster activities for 5-year-olds:")
        for point in response[0]:
            name = point.payload.get('name', 'N/A')
            ages = f"{point.payload.get('min_age_years')}-{point.payload.get('max_age_years')}"
            price = point.payload.get('price_numeric', 'N/A')
            print(f"  - {name} (Ages {ages}) - ${price}")
        
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(test_small_ingestion())
