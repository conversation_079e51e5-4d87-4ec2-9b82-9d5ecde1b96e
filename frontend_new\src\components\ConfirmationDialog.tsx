import React from 'react';
import { Check, X } from 'lucide-react';

interface ConfirmationDialogProps {
  plan: string;
  confirmationId: string;
  onConfirm: (confirmationId: string, decision: 'yes' | 'no') => void;
  onCancel?: () => void;
}

const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  plan,
  confirmationId,
  onConfirm,
  onCancel
}) => {
  const handleConfirm = () => {
    onConfirm(confirmationId, 'yes');
  };

  const handleDecline = () => {
    onConfirm(confirmationId, 'no');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="bg-primary-500 text-white px-6 py-4">
          <h3 className="text-lg font-semibold">Confirm Your Search Plan</h3>
          <p className="text-primary-100 text-sm mt-1">
            Please review the plan below and confirm if you'd like to proceed
          </p>
        </div>

        {/* Plan Content */}
        <div className="px-6 py-4 max-h-96 overflow-y-auto">
          <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
            <h4 className="font-medium text-gray-900 mb-3">Search Plan:</h4>
            <div className="text-gray-700 whitespace-pre-wrap leading-relaxed">
              {plan}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex gap-3 justify-end">
          {onCancel && (
            <button
              onClick={onCancel}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Cancel
            </button>
          )}
          <button
            onClick={handleDecline}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors flex items-center gap-2"
          >
            <X className="w-4 h-4" />
            No, Cancel
          </button>
          <button
            onClick={handleConfirm}
            className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors flex items-center gap-2"
          >
            <Check className="w-4 h-4" />
            Yes, Proceed
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationDialog; 