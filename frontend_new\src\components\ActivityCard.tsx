import React, { useState } from 'react';
import { ExternalLink, Download } from 'lucide-react';
import { useAppStore } from '../stores/appStore';
import { API_BASE_URL } from '../config';

export interface Activity {
  name: string;
  description?: string;
  provider?: string;
  location_area?: string;
  age_range?: string;
  activity_type?: string;
  season?: string;
  registration_period?: string;
  extracted_from?: string;
  schedule?: string;
  registrationInfo?: string;
  contactInfo?: string;
  location?: string;
  status?: string;
  website?: string;
}

interface ActivityCardProps {
  activities: Activity[];
  className?: string;
}

export function ActivityCard({ activities, className = '' }: ActivityCardProps) {
  const [isExporting, setIsExporting] = useState(false);
  const { sessionId } = useAppStore();

  if (!activities || activities.length === 0) {
    return null;
  }

  const exportToCalendar = async (activity: Activity) => {
    if (isExporting || !sessionId) return;
    setIsExporting(true);
    try {
      // Extract program dates from activity data
      const programDates = [];
      
      // Parse schedule information if available
      if (activity.schedule) {
        // Simple parsing - in real app this would be more sophisticated
        const scheduleText = activity.schedule.toLowerCase();
        
        // Look for date patterns and create sample program dates
        if (scheduleText.includes('june') || scheduleText.includes('summer')) {
          programDates.push({
            date: '2025-06-15',
            time: '10:00',
            duration: 90
          });
          programDates.push({
            date: '2025-06-22',
            time: '10:00', 
            duration: 90
          });
        }
      }
      
      const response = await fetch(`${API_BASE_URL}/api/activity/export-calendar`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: activity.name,
          provider: activity.provider,
          location: activity.location,
          registration_date: activity.registrationInfo?.includes('March') ? '2025-03-01' : '2025-04-01',
          program_dates: programDates,
          notes: `${activity.description || ''}\n\nAge Range: ${activity.age_range}\nContact: ${activity.contactInfo || 'See provider website'}`,
        }),
      });

      if (!response.ok) {
        throw new Error('Export failed');
      }

      // Create download link
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${activity.name.replace(/[^a-zA-Z0-9]/g, '_')}_${activity.provider?.replace(/[^a-zA-Z0-9]/g, '_')}.ics`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Failed to export calendar:', err);
      alert('Failed to export to calendar. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className={`bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-center mb-3">
        <div className="bg-blue-500 text-white rounded-full p-2 mr-3">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-blue-800">
          BC Activities Found
        </h3>
        <span className="ml-auto bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
          {activities.length} {activities.length === 1 ? 'program' : 'programs'}
        </span>
      </div>

      <div className="space-y-3">
        {activities.map((activity, index) => (
          <div key={index} className="bg-white rounded-lg p-3 border border-gray-200 shadow-sm">
            <div className="flex justify-between items-start mb-3">
              <div>
                <h4 className="font-semibold text-gray-900 mb-1">
                  {activity.name}
                </h4>
                
                {activity.description && (
                  <p className="text-gray-700 text-sm mb-2 leading-relaxed">
                    {activity.description}
                  </p>
                )}

                <div className="flex flex-wrap gap-2 text-xs">
                  {activity.provider && (
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full">
                      📍 {activity.provider}
                    </span>
                  )}
                  
                  {activity.age_range && (
                    <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full">
                      👶 Ages {activity.age_range}
                    </span>
                  )}
                  
                  {activity.activity_type && (
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                      🏃 {activity.activity_type}
                    </span>
                  )}
                  
                  {activity.season && (
                    <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded-full">
                      🗓️ {activity.season}
                    </span>
                  )}
                  
                  {activity.location_area && (
                    <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded-full">
                      📍 {activity.location_area}
                    </span>
                  )}
                </div>

                {activity.registration_period && (
                  <div className="mt-2 text-xs text-amber-700 bg-amber-50 px-2 py-1 rounded border border-amber-200">
                    ⏰ Registration: {activity.registration_period}
                  </div>
                )}
              </div>
              
              <div className="flex gap-2">
                {activity.website && (
                  <button
                    onClick={() => window.open(activity.website, '_blank')}
                    className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    title="Visit website"
                  >
                    <ExternalLink className="w-4 h-4" />
                  </button>
                )}
                
                <button
                  onClick={() => exportToCalendar(activity)}
                  className="p-2 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                  title="Export to calendar"
                >
                  <Download className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-3 text-xs text-gray-500 text-center">
        💡 Tip: Visit provider websites for current schedules and registration details
      </div>
    </div>
  );
}

export default ActivityCard; 