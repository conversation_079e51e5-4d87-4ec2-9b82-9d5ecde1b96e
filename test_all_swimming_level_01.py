#!/usr/bin/env python3

import asyncio
from qdrant_client import AsyncQdrantClient, models
from config import AgentConfig

async def test_all_swimming_level_01():
    """Test to find all 160 Swimming Level 01 activities"""
    
    client = AsyncQdrantClient(
        url=AgentConfig.QDRANT_URL,
        api_key=AgentConfig.QDRANT_API_KEY,
    )
    
    try:
        print("🏊 COMPREHENSIVE SWIMMING LEVEL 01 TEST")
        print("="*60)
        
        # 1. Search for all Swimming Level 01 activities
        print("Searching for ALL Swimming Level 01 activities...")
        
        all_swimming_01 = []
        offset = None
        
        while True:
            response = await client.scroll(
                collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
                scroll_filter=models.Filter(
                    must=[
                        models.FieldCondition(key="name", match=models.MatchText(text="Swimming Level 01"))
                    ]
                ),
                limit=100,  # Get 100 at a time
                offset=offset,
                with_payload=True
            )
            
            points, next_offset = response
            all_swimming_01.extend(points)
            
            if next_offset is None:
                break
            offset = next_offset
        
        print(f"✅ Found {len(all_swimming_01)} Swimming Level 01 activities total")
        
        # 2. Analyze by subcategory
        print(f"\n📊 BREAKDOWN BY SUBCATEGORY:")
        subcategories = {}
        facilities = {}
        age_ranges = {}
        cities = {}
        
        for point in all_swimming_01:
            payload = point.payload
            name = payload.get('name', '')
            facility = payload.get('facility', 'Unknown')
            city = payload.get('city', 'Unknown')
            min_age = payload.get('min_age_years')
            max_age = payload.get('max_age_years')
            
            # Extract subcategory from name
            if 'Preschool' in name:
                subcategory = 'Preschool'
            elif 'Children' in name:
                subcategory = 'Children'
            elif 'Tots' in name:
                subcategory = 'Tots - Beginner'
            elif 'Adult and Youth' in name:
                subcategory = 'Adult and Youth'
            elif 'Caregiver Participation' in name:
                subcategory = 'Caregiver Participation'
            else:
                subcategory = 'Other'
            
            subcategories[subcategory] = subcategories.get(subcategory, 0) + 1
            facilities[facility] = facilities.get(facility, 0) + 1
            cities[city] = cities.get(city, 0) + 1
            
            age_key = f"{min_age}-{max_age}"
            age_ranges[age_key] = age_ranges.get(age_key, 0) + 1
        
        for subcategory, count in sorted(subcategories.items()):
            print(f"  {subcategory}: {count} activities")
        
        print(f"\n🏢 BREAKDOWN BY FACILITY:")
        for facility, count in sorted(facilities.items()):
            print(f"  {facility}: {count} activities")
        
        print(f"\n🏙️ BREAKDOWN BY CITY:")
        for city, count in sorted(cities.items()):
            print(f"  {city}: {count} activities")
        
        print(f"\n👶 BREAKDOWN BY AGE RANGE:")
        for age_range, count in sorted(age_ranges.items()):
            print(f"  Ages {age_range}: {count} activities")
        
        # 3. Show sample activities from each subcategory
        print(f"\n📋 SAMPLE ACTIVITIES BY SUBCATEGORY:")
        shown_subcategories = set()
        
        for point in all_swimming_01[:20]:  # Show first 20 as samples
            payload = point.payload
            name = payload.get('name', '')
            
            subcategory = 'Other'
            if 'Preschool' in name:
                subcategory = 'Preschool'
            elif 'Children' in name:
                subcategory = 'Children'
            elif 'Tots' in name:
                subcategory = 'Tots - Beginner'
            elif 'Adult and Youth' in name:
                subcategory = 'Adult and Youth'
            elif 'Caregiver Participation' in name:
                subcategory = 'Caregiver Participation'
            
            if subcategory not in shown_subcategories:
                print(f"\n  {subcategory} Example:")
                print(f"    Name: {name}")
                print(f"    Facility: {payload.get('facility')}")
                print(f"    Ages: {payload.get('min_age_years')}-{payload.get('max_age_years')}")
                print(f"    Start Date: {payload.get('start_date')}")
                print(f"    Days: {payload.get('days_of_week_list')}")
                print(f"    Price: ${payload.get('price_numeric', 'N/A')}")
                shown_subcategories.add(subcategory)
        
        # 4. Test age-based filtering for 5-year-olds
        print(f"\n👶 TESTING AGE-BASED FILTERING (5-year-olds):")
        response_5yo = await client.scroll(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            scroll_filter=models.Filter(
                must=[
                    models.FieldCondition(key="name", match=models.MatchText(text="Swimming Level 01")),
                    models.FieldCondition(key="min_age_years", range=models.Range(lte=5)),
                    models.FieldCondition(key="max_age_years", range=models.Range(gte=5))
                ]
            ),
            limit=50,
            with_payload=True
        )
        
        print(f"Found {len(response_5yo[0])} Swimming Level 01 activities suitable for 5-year-olds")
        
        # 5. Summary and expectations
        print(f"\n🎯 SUMMARY:")
        print(f"✅ Total Swimming Level 01 activities: {len(all_swimming_01)}")
        print(f"✅ Expected: ~160 activities")
        print(f"✅ Coverage: {len(all_swimming_01)/160*100:.1f}% of expected")
        
        if len(all_swimming_01) >= 150:
            print(f"🎉 EXCELLENT: Found {len(all_swimming_01)} activities - very close to expected 160!")
        elif len(all_swimming_01) >= 100:
            print(f"✅ GOOD: Found {len(all_swimming_01)} activities - substantial improvement!")
        else:
            print(f"⚠️  PARTIAL: Found {len(all_swimming_01)} activities - may need further investigation")
        
        print(f"\n💡 NEXT STEPS:")
        if len(all_swimming_01) >= 150:
            print(f"✅ Ready to test agent with comprehensive swimming class queries!")
            print(f"✅ Should provide detailed Swimming Levels 01-06 responses")
        else:
            print(f"🔍 May need to investigate why some activities are missing")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(test_all_swimming_level_01())
