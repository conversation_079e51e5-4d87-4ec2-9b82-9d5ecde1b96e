FROM python:3.12-slim
WORKDIR /app

# Copy the local graphiti package first so it is available for requirements installation
# This layer only changes if the graphiti directory contents change
COPY ./graphiti /app/graphiti

# Copy requirements next and install them
# The relative path "./graphiti" inside requirements.txt now resolves correctly
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy only the application source to keep the image lean and avoid permission problems
COPY ./multi_tool_agent /app/multi_tool_agent

# Copy the FastAPI server entrypoint
COPY adk_server.py /app/adk_server.py

# Create a non-root user and set permissions for the app directory
# Do this after all files are copied to /app
RUN adduser --disabled-password --gecos "" myuser && \
    chown -R myuser:myuser /app

USER myuser

ENV PATH="/home/<USER>/.local/bin:$PATH"
# ENV PORT=8080 - Removed as Cloud Run sets this automatically

# Start the FastAPI server with Uvicorn, binding to the Cloud Run provided $PORT
CMD ["sh", "-c", "uvicorn adk_server:app --host 0.0.0.0 --port ${PORT:-8080}"]
