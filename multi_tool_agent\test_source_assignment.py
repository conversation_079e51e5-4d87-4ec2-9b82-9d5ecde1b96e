#!/usr/bin/env python3

import json
import os
from ingestion.data_utils import normalize_activity

def test_source_assignment():
    """Test if source assignment and city inference works correctly"""
    
    print("🧪 TESTING SOURCE ASSIGNMENT AND CITY INFERENCE")
    print("="*60)
    
    # Load a few activities from the JSON
    json_path = os.path.join(os.path.dirname(__file__), 'ingestion', 'fast_dropin_activities.json')
    with open(json_path, 'r', encoding='utf-8') as f:
        activities = json.load(f)
    
    # Take first 3 activities for testing
    test_activities = activities[:3]
    
    print("BEFORE source assignment:")
    for i, activity in enumerate(test_activities):
        print(f"{i+1}. {activity.get('name')}")
        print(f"   Source: {activity.get('source', 'NOT SET')}")
        print(f"   Facility: {activity.get('facility')}")
        print()
    
    # Simulate the source assignment from update_qdrant_activities.py
    for activity in test_activities:
        activity['source'] = 'New West PerfectMind'
    
    print("AFTER source assignment:")
    for i, activity in enumerate(test_activities):
        print(f"{i+1}. {activity.get('name')}")
        print(f"   Source: {activity.get('source')}")
        print(f"   Source (lowercase): {activity.get('source', '').lower()}")
        print(f"   Contains 'new west': {'new west' in activity.get('source', '').lower()}")
        print()
    
    print("AFTER normalize_activity():")
    for i, activity in enumerate(test_activities):
        normalized = normalize_activity(activity)
        print(f"{i+1}. {normalized.get('name')}")
        print(f"   Source: {normalized.get('source')}")
        print(f"   City: {normalized.get('city')}")
        print(f"   Facility: {normalized.get('facility')}")
        print()
    
    # Test specific Swimming Level 01 activities
    swimming_01_activities = [
        act for act in activities 
        if 'Swimming Level 01' in act.get('name', '')
    ][:3]
    
    print("TESTING SWIMMING LEVEL 01 ACTIVITIES:")
    print("-" * 40)
    
    for activity in swimming_01_activities:
        activity['source'] = 'New West PerfectMind'
        normalized = normalize_activity(activity)
        print(f"Name: {normalized.get('name')}")
        print(f"City: {normalized.get('city')}")
        print(f"Age: {normalized.get('min_age_years')}-{normalized.get('max_age_years')}")
        print(f"Category: {normalized.get('category')}")
        print(f"Facility: {normalized.get('facility')}")
        print()

if __name__ == "__main__":
    test_source_assignment()
