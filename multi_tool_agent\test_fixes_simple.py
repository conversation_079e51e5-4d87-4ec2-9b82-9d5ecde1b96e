#!/usr/bin/env python3

import json
import os
from ingestion.data_utils import normalize_activity

def test_data_fixes():
    """Test our data processing fixes"""
    
    print("🔧 TESTING DATA PROCESSING FIXES")
    print("="*50)
    
    # Load some activities from the JSON
    json_path = os.path.join(os.path.dirname(__file__), 'ingestion', 'fast_dropin_activities.json')
    with open(json_path, 'r', encoding='utf-8') as f:
        activities = json.load(f)
    
    # Test 1: Source assignment and city inference
    print("Test 1: Source assignment and city inference")
    print("-" * 40)
    
    test_activities = activities[:5]
    
    for activity in test_activities:
        # Simulate the source assignment from update_qdrant_activities.py
        activity['source'] = 'New West PerfectMind'
        
        # Test normalization
        normalized = normalize_activity(activity)
        
        print(f"✓ {normalized.get('name')}")
        print(f"  Source: {normalized.get('source')}")
        print(f"  City: {normalized.get('city')}")
        print(f"  Facility: {normalized.get('facility', 'NOT SET')}")
        print()
    
    # Test 2: Swimming Level 01 activities specifically
    print("Test 2: Swimming Level 01 activities")
    print("-" * 40)
    
    swimming_01_activities = [
        act for act in activities 
        if 'Swimming Level 01' in act.get('name', '')
    ][:5]
    
    print(f"Found {len(swimming_01_activities)} Swimming Level 01 activities in JSON")
    
    for activity in swimming_01_activities:
        activity['source'] = 'New West PerfectMind'
        normalized = normalize_activity(activity)
        
        print(f"✓ {normalized.get('name')}")
        print(f"  City: {normalized.get('city')}")
        print(f"  Ages: {normalized.get('min_age_years')}-{normalized.get('max_age_years')}")
        print(f"  Category: {normalized.get('category')}")
        print(f"  Facility: {normalized.get('facility', 'NOT SET')}")
        print(f"  Start Date: {normalized.get('start_date')}")
        print(f"  Days: {normalized.get('days_of_week_list')}")
        print()
    
    # Test 3: Check for activities missing facility
    print("Test 3: Activities with missing facility info")
    print("-" * 40)
    
    missing_facility = []
    for activity in activities[:50]:
        activity['source'] = 'New West PerfectMind'
        normalized = normalize_activity(activity)
        
        if not normalized.get('facility'):
            missing_facility.append(normalized)
    
    print(f"Found {len(missing_facility)} activities missing facility info:")
    for activity in missing_facility[:5]:  # Show first 5
        print(f"- {activity.get('name')}")
        print(f"  General Location: {activity.get('general_location', 'N/A')}")
        print()
    
    # Test 4: Summary statistics
    print("Test 4: Summary Statistics")
    print("-" * 40)
    
    total_tested = 50
    activities_with_source = 0
    activities_with_city = 0
    activities_with_facility = 0
    swimming_level_01_count = 0
    
    for activity in activities[:total_tested]:
        activity['source'] = 'New West PerfectMind'
        normalized = normalize_activity(activity)
        
        if normalized.get('source'):
            activities_with_source += 1
        if normalized.get('city') == 'New Westminster':
            activities_with_city += 1
        if normalized.get('facility'):
            activities_with_facility += 1
        if 'Swimming Level 01' in normalized.get('name', ''):
            swimming_level_01_count += 1
    
    print(f"Out of {total_tested} activities tested:")
    print(f"✓ {activities_with_source} have source field set")
    print(f"✓ {activities_with_city} have city = 'New Westminster'")
    print(f"✓ {activities_with_facility} have facility field set")
    print(f"✓ {swimming_level_01_count} are Swimming Level 01 activities")
    
    print(f"\n🎯 Expected improvements after re-ingestion:")
    print(f"- All {total_tested} activities should have city = 'New Westminster'")
    print(f"- Most activities should have facility field populated")
    print(f"- Swimming Level 01 activities should be searchable by age and location")

if __name__ == "__main__":
    test_data_fixes()
