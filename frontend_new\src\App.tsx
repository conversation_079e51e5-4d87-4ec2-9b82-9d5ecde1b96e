import React from 'react'
import Chat<PERSON>ontainer from './components/ChatContainer'
import { Sidebar } from './components/Sidebar'
import Header from './components/Header'
import GoogleSignIn from './components/GoogleSignIn'
import { useAppStore, generateSessionId } from './stores/appStore'
import { useWebSocketConnection } from './hooks/useWebSocketConnection'
import './index.css'

function App() {
  const { sessionId, setSessionId, sidebarOpen, toggleSidebar, auth, checkAuthStatus } = useAppStore()
  const { connectionStatus } = useWebSocketConnection()

  // Check authentication status on app load
  React.useEffect(() => {
    checkAuthStatus()
  }, [checkAuthStatus])

  // Initialize session on mount (only when authenticated)
  React.useEffect(() => {
    if (auth.isAuthenticated && !sessionId) {
      const newSessionId = generateSessionId()
      setSessionId(newSessionId)
      console.log('🆔 Generated new session ID:', newSessionId)
    }
  }, [auth.isAuthenticated, sessionId, setSessionId])

  // Show sign-in screen if not authenticated
  if (!auth.isAuthenticated) {
    return <GoogleSignIn />
  }

  // Show main app if authenticated
  return (
    <div className="h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <Sidebar isOpen={sidebarOpen} onToggle={toggleSidebar} />
      
      {/* Main Content */}
      <div className={`flex-1 flex flex-col transition-all duration-300 ${sidebarOpen ? 'ml-0' : 'ml-0'}`}>
        {/* Header */}
        <Header connectionStatus={connectionStatus} />
        
        {/* Error Display - temporarily commented out */}
        {/* {error && <ErrorDisplay error={error} />} */}
        
        {/* Chat Area */}
        <div className="flex-1 overflow-hidden">
          <ChatContainer />
        </div>
      </div>
    </div>
  )
}

export default App
