#!/usr/bin/env python3

import asyncio
from qdrant_client import AsyncQdrant<PERSON>lient, models
from config import <PERSON><PERSON>onfig

async def quick_test():
    """Quick test to verify the improved data ingestion"""
    
    client = AsyncQdrantClient(
        url=AgentConfig.QDRANT_URL,
        api_key=AgentConfig.QDRANT_API_KEY,
    )
    
    try:
        print("🧪 TESTING IMPROVED DATA INGESTION")
        print("="*50)
        
        # 1. Get total count
        collection_info = await client.get_collection(AgentConfig.QDRANT_COLLECTION_NAME)
        print(f"Total activities in database: {collection_info.points_count}")
        
        # 2. Test New Westminster city search
        print("\n🔍 TESTING NEW WESTMINSTER CITY SEARCH")
        response = await client.scroll(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            scroll_filter=models.Filter(
                must=[
                    models.FieldCondition(key="city", match=models.MatchValue(value="New Westminster"))
                ]
            ),
            limit=10,
            with_payload=True
        )
        
        print(f"Found {len(response[0])} New Westminster activities (showing first 10):")
        for i, point in enumerate(response[0]):
            payload = point.payload
            print(f"{i+1}. {payload.get('name')}")
            print(f"   Facility: {payload.get('facility')}")
            print(f"   Category: {payload.get('category')}")
        
        # 3. Test Swimming Level 01 search
        print("\n🏊 TESTING SWIMMING LEVEL 01 SEARCH")
        response2 = await client.scroll(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            scroll_filter=models.Filter(
                must=[
                    models.FieldCondition(key="name", match=models.MatchText(text="Swimming Level 01"))
                ]
            ),
            limit=5,
            with_payload=True
        )
        
        print(f"Found {len(response2[0])} Swimming Level 01 activities (showing first 5):")
        for i, point in enumerate(response2[0]):
            payload = point.payload
            print(f"{i+1}. {payload.get('name')}")
            print(f"   City: {payload.get('city')}")
            print(f"   Ages: {payload.get('min_age_years')}-{payload.get('max_age_years')}")
            print(f"   Facility: {payload.get('facility')}")
        
        # 4. Test age-based search for 5-year-olds
        print("\n👶 TESTING AGE-BASED SEARCH (5-year-olds)")
        response3 = await client.scroll(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            scroll_filter=models.Filter(
                must=[
                    models.FieldCondition(key="city", match=models.MatchValue(value="New Westminster")),
                    models.FieldCondition(key="min_age_years", range=models.Range(lte=5)),
                    models.FieldCondition(key="max_age_years", range=models.Range(gte=5))
                ]
            ),
            limit=5,
            with_payload=True
        )
        
        print(f"Found {len(response3[0])} activities for 5-year-olds in New Westminster:")
        for i, point in enumerate(response3[0]):
            payload = point.payload
            print(f"{i+1}. {payload.get('name')}")
            print(f"   Ages: {payload.get('min_age_years')}-{payload.get('max_age_years')}")
            print(f"   Category: {payload.get('category')}")
        
        print(f"\n✅ SUCCESS INDICATORS:")
        print(f"✅ Total points: {collection_info.points_count} (should be ~7,000)")
        print(f"✅ New Westminster activities: {len(response[0])} found (should be 10+)")
        print(f"✅ Swimming Level 01: {len(response2[0])} found (should be 5+)")
        print(f"✅ Age-appropriate activities: {len(response3[0])} found (should be 5+)")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(quick_test())
