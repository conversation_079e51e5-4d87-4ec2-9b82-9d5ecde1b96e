import { useEffect, useRef } from 'react';
import { useAppStore } from '../stores/appStore';
import { MessageContent } from './MessageContent';
import { ActivityCard } from './ActivityCard';
import type { Message } from '../types/adk';

const MessageList = () => {
  const { messages, isAgentTyping, discoveredActivities } = useAppStore();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Auto-scroll when messages change or agent is typing
  useEffect(() => {
    scrollToBottom();
  }, [messages, isAgentTyping]);

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div 
      ref={scrollContainerRef}
      className="flex-1 overflow-y-auto p-4 space-y-4 scroll-smooth scrollbar-thin"
    >
      {messages.length === 0 && !isAgentTyping && (
        <div className="text-center text-gray-500 py-8">
          <h3 className="text-lg font-medium mb-2">Welcome to BC Activity Assistant! 👋</h3>
          <p className="text-sm">Ask me about children's activities in British Columbia.</p>
          <p className="text-xs mt-1">Try: "Find swimming lessons for a 6-year-old in Vancouver"</p>
        </div>
      )}

      {messages.map((message: Message) => (
        <div key={message.id}>
          <div className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div className={`max-w-md lg:max-w-lg xl:max-w-xl ${message.role === 'user' ? 'message-user' : 'message-assistant'}`}>
              <MessageContent 
                content={message.content} 
                isAgent={message.role === 'assistant'} 
              />
              <div className={`text-xs mt-1 opacity-70 ${message.role === 'user' ? 'text-white' : 'text-gray-500'}`}>
                {formatTime(message.timestamp)}
                {!message.isComplete && <span className="ml-1 typing-indicator">●</span>}
              </div>
            </div>
          </div>
          
          {/* Show activities after assistant messages */}
          {message.role === 'assistant' && message.isComplete && discoveredActivities.length > 0 && (
            <div className="mt-3">
              <ActivityCard activities={discoveredActivities} />
            </div>
          )}
        </div>
      ))}

      {isAgentTyping && messages[messages.length - 1]?.role !== 'assistant' && (
        <div className="flex justify-start">
          <div className="message-assistant">
            <div className="flex items-center gap-1">
              <span className="typing-indicator">●</span>
              <span className="typing-indicator" style={{ animationDelay: '0.2s' }}>●</span>
              <span className="typing-indicator" style={{ animationDelay: '0.4s' }}>●</span>
            </div>
          </div>
        </div>
      )}

      {/* Invisible element to scroll to */}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList; 