import React from 'react';

interface ToolStatusProps {
  isActive: boolean;
  tool?: string;
  message?: string;
  estimatedDuration?: string;
  className?: string;
}

const getToolIcon = (tool: string) => {
  const icons: Record<string, string> = {
    'retrieve_info_from_memory': '🧠',
    'url_context_search': '🔍',
    'store_activity_preference': '💾',
    'store_registration_info': '📝',
    'get_proactive_suggestions': '💡',
    'get_current_time': '🕐',
    'authenticate_with_google': '🔐',
  };
  return icons[tool] || '🛠️';
};

const getToolDescription = (tool: string) => {
  const descriptions: Record<string, string> = {
    'retrieve_info_from_memory': 'Checking what I know about your family',
    'url_context_search': 'Searching for current BC activities',
    'store_activity_preference': 'Saving your preferences',
    'store_registration_info': 'Recording registration information',
    'get_proactive_suggestions': 'Finding seasonal recommendations',
    'get_current_time': 'Getting current BC time',
    'authenticate_with_google': 'Authenticating with Google',
  };
  return descriptions[tool] || `Using ${tool}`;
};

export function ToolStatus({ isActive, tool, message, estimatedDuration, className = '' }: ToolStatusProps) {
  if (!isActive) {
    return null;
  }

  const toolIcon = tool ? getToolIcon(tool) : '⚙️';
  const toolDescription = message || (tool ? getToolDescription(tool) : 'Processing...');

  return (
    <div className={`flex justify-start ${className}`}>
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 px-4 py-3 rounded-lg shadow-sm max-w-md">
        <div className="flex items-center space-x-3">
          {/* Animated spinner */}
          <div className="relative">
            <div className="animate-spin rounded-full h-5 w-5 border-2 border-blue-200">
              <div className="absolute top-0 left-0 h-5 w-5 border-2 border-blue-500 rounded-full border-t-transparent animate-spin"></div>
            </div>
          </div>
          
          {/* Tool icon */}
          <span className="text-lg" role="img" aria-label="tool">
            {toolIcon}
          </span>
          
          {/* Tool information */}
          <div className="flex-1 min-w-0">
            <div className="text-sm font-medium text-blue-900 truncate">
              {toolDescription}
            </div>
            
            {tool && (
              <div className="text-xs text-blue-600 mt-0.5">
                <span className="bg-blue-100 px-2 py-0.5 rounded-full">
                  {tool.replace(/_/g, ' ')}
                </span>
              </div>
            )}
            
            {estimatedDuration && (
              <div className="text-xs text-gray-500 mt-1">
                ⏱️ {estimatedDuration}
              </div>
            )}
          </div>
        </div>
        
        {/* Progress bar animation */}
        <div className="mt-2 w-full bg-blue-100 rounded-full h-1 overflow-hidden">
          <div className="bg-blue-500 h-1 rounded-full w-1/3 animate-pulse transform translate-x-0 transition-transform duration-1000"></div>
        </div>
      </div>
    </div>
  );
}

export default ToolStatus; 