from pydantic import BaseModel, Field
from typing import Optional, List

class ActivityFilters(BaseModel):
    """
    A structured set of filters for searching activities.
    All parameters are optional.
    """
    age: Optional[int] = Field(
        None, 
        description="The specific age of the child to filter for."
    )
    location: Optional[str] = Field(
        None, 
        description="The city or general area, e.g., 'Burnaby' or 'New West'."
    )
    day_of_week: Optional[List[str]] = Field(
        default_factory=list,
        description="A list of days of the week, e.g., ['thursday', 'saturday']."
    )
    max_price: Optional[float] = Field(
        None, 
        description="The maximum price for the activity."
    )
    date: Optional[str] = Field(
        None, 
        description="A specific date in 'YYYY-MM-DD' format to check for availability."
    )
    is_open: Optional[bool] = Field(
        None, 
        description="Filter for activities that are currently open for registration."
    )
    name_contains: Optional[str] = Field(
        None,
        description="Filter for activities whose name contains specific keywords (e.g., 'beginner', 'level 1', 'intro')."
    )
