apiVersion: apps/v1
kind: Deployment
metadata:
  name: multi-tool-agent-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: multi-tool-agent
  template:
    metadata:
      labels:
        app: multi-tool-agent
    spec:
      containers:
      - name: multi-tool-agent-container
        imagePullPolicy: Always
        # This image URL is a placeholder that will be replaced by the gcloud build command
        image: agent_google:latest
        resources:
          requests:
            memory: "512Mi"
            cpu: "1000m"
          limits:
            memory: "1Gi"
            cpu: "2000m"
        ports:
        - containerPort: 8080
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 10
        env:
          - name: PORT
            value: "8080"
          - name: GOOGLE_GENAI_USE_VERTEXAI
            value: "true"
          # Set these values from your environment or replace them directly.
          # For production, use Kubernetes Secrets: https://kubernetes.io/docs/concepts/configuration/secret/
          - name: GOOGLE_CLOUD_PROJECT
            value: "YOUR_GCP_PROJECT_ID"
          - name: GOOGLE_CLOUD_LOCATION
            value: "YOUR_GCP_LOCATION"
          - name: NEO4J_URI
            value: "YOUR_NEO4J_URI"
          - name: NEO4J_USER
            value: "YOUR_NEO4J_USER"
          - name: NEO4J_PASSWORD
            value: "YOUR_NEO4J_PASSWORD"
          - name: GOOGLE_API_KEY
            value: "YOUR_GOOGLE_API_KEY"
          - name: QDRANT_URL
            value: "YOUR_QDRANT_URL"
          - name: QDRANT_API_KEY
            value: "YOUR_QDRANT_API_KEY"
          - name: JWT_SECRET
            value: "YOUR_JWT_SECRET"
          - name: GOOGLE_CLIENT_ID
            value: "YOUR_GOOGLE_CLIENT_ID"
---
apiVersion: v1
kind: Service
metadata:
  name: multi-tool-agent-service
spec:
  type: LoadBalancer
  ports:
    - port: 80
      targetPort: 8080
  selector:
    app: multi-tool-agent
