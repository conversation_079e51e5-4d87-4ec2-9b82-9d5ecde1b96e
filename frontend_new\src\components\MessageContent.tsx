import React from 'react';

interface MessageContentProps {
  content: string;
  isAgent?: boolean;
}

// ↓↓↓ ADD: helper to sanitize agent output by removing internal markers
const sanitizeAgentContent = (text: string): string => {
  const FINAL_MARKER = '/*FINAL_ANSWER*/';

  // If the final answer marker exists, show only the section after it
  if (text.includes(FINAL_MARKER)) {
    return text.substring(text.indexOf(FINAL_MARKER) + FINAL_MARKER.length).trim();
  }

  // Replace literal <br> tags with newline before further processing
  const cleaned = text.replace(/<br\s*\/?\s*>/gi, '\n');

  return cleaned
    .split('\n')
    .filter((line) => {
      const trimmed = line.trim();
      return !(trimmed.startsWith('/*') && trimmed.endsWith('*/'));
    })
    .join('\n');
};

export function MessageContent({ content, isAgent = false }: MessageContentProps) {
  if (!isAgent) {
    // User messages - simple text
    return <div className="text-gray-800">{content}</div>;
  }

  // Agent messages - enhanced formatting
  const sanitizedContent = sanitizeAgentContent(content);

  const renderWithLinks = (segment: string, key: number) => {
    // Replace markdown links with <a>
    const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
    const parts: React.ReactNode[] = [];
    let lastIndex = 0;
    let match;
    while ((match = linkRegex.exec(segment)) !== null) {
      const [full, label, href] = match;
      // If the label itself looks like a URL, shorten it to "link"
      const displayText = /^https?:\/\//i.test(label) ? 'link' : label;
      if (match.index > lastIndex) {
        parts.push(segment.slice(lastIndex, match.index));
      }
      parts.push(
        <a key={`link-${key}-${match.index}`} href={href} target="_blank" rel="noopener noreferrer" className="text-blue-600 underline">
          {displayText}
        </a>
      );
      lastIndex = match.index + full.length;
    }
    if (lastIndex < segment.length) {
      parts.push(segment.slice(lastIndex));
    }
    return parts;
  };

  const formatAgentContent = (text: string) => {
    // First, split by actual line breaks if they exist
    const lines = text.split('\n');
    
    return lines.map((line, lineIndex) => {
      // Skip empty lines
      if (!line.trim()) {
        return <br key={lineIndex} />;
      }
      
      // Handle bold text like **Level 1 — Newbees:**
      if (line.includes('**') && line.includes(':**')) {
        const parts = line.split('**');
        return (
          <div key={lineIndex} className="mb-3">
            {parts.map((part, partIndex) => {
              if (partIndex % 2 === 1 && part.includes(':**')) {
                return (
                  <strong key={partIndex} className="font-semibold text-blue-700 text-base">
                    {part.replace(':**', ':')}
                  </strong>
                );
              }
              return <span key={partIndex}>{renderWithLinks(part, partIndex)}</span>;
            })}
          </div>
        );
      }
      
      // Handle bullet points starting with *
      if (/^\*\s+/.test(line.trim())) {
        const textPart = line.trim().replace(/^\*\s+/, '');
        return (
          <div key={lineIndex} className="ml-4 mb-2 flex">
            <span className="text-blue-600 mr-2">•</span>
            <span className="text-gray-700">{renderWithLinks(textPart, 0)}</span>
          </div>
        );
      }
      
      // Handle numbered list like "1. "
      const numMatch = line.trim().match(/^(\d+)\.\s+(.*)$/);
      if (numMatch) {
        const [, num, rest] = numMatch;
        return (
          <div key={lineIndex} className="ml-4 mb-2 flex">
            <span className="text-gray-500 mr-2">{num}.</span>
            <span className="text-gray-700">{renderWithLinks(rest, 0)}</span>
          </div>
        );
      }
      
      // For long paragraphs, split into logical sentences and groups
      if (line.length > 150) {
        return (
          <div key={lineIndex} className="space-y-2">
            {formatLongParagraph(line)}
          </div>
        );
      }
      
      // Regular shorter paragraphs
      return (
        <p key={lineIndex} className="mb-3 leading-relaxed text-gray-800">
          {renderWithLinks(line, 0)}
        </p>
      );
    });
  };

  const formatLongParagraph = (text: string) => {
    // Split by sentences (periods followed by space and capital letter)
    const sentences = text.split(/\. (?=[A-Z])/);
    const groups: string[] = [];
    let currentGroup = '';
    
    sentences.forEach((sentence, index) => {
      // Add the period back except for the last sentence
      const sentenceWithPeriod = index < sentences.length - 1 ? sentence + '.' : sentence;
      
      // Group sentences into logical paragraphs (2-3 sentences max)
      if (currentGroup === '') {
        currentGroup = sentenceWithPeriod;
      } else if (currentGroup.length + sentenceWithPeriod.length < 200) {
        currentGroup += ' ' + sentenceWithPeriod;
      } else {
        groups.push(currentGroup);
        currentGroup = sentenceWithPeriod;
      }
    });
    
    // Add the last group
    if (currentGroup) {
      groups.push(currentGroup);
    }
    
    return groups.map((group, index) => (
      <p key={index} className="mb-3 leading-relaxed text-gray-800">
        {group}
      </p>
    ));
  };

  return (
    <div className="max-w-none text-gray-800">
      {formatAgentContent(sanitizedContent)}
    </div>
  );
}

export default MessageContent; 