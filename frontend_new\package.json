{"name": "frontend_new", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:local": "cross-env VITE_API_BASE_URL=http://localhost:8080 vite", "dev:cloud": "cross-env VITE_API_BASE_URL=https://bc-activity-agent-REPLACE.a.run.app vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@types/eventsource": "^1.1.15", "autoprefixer": "^10.4.21", "lucide-react": "^0.513.0", "postcss": "^8.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^3.4.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "cross-env": "^7.0.3"}}