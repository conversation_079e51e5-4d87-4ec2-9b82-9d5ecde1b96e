import logging
from google.adk.agents import Agent, SequentialAgent
from google.adk.tools import google_search
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List

# Corrected import path for schemas
from .tools.schemas import ActivityFilters
from .tools import AgentTool, raw_activity_search, user_confirmation_tool
from .config import AgentConfig

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Pydantic Schemas ---
class SearchArgs(BaseModel):
    query: str
    filters: ActivityFilters
    find_back_to_back: bool

class SynthesizerInput(BaseModel):
    user_request: str
    raw_search_results: List[Dict[str, Any]]

# --- Query Classification Schema ---
class QueryClassification(BaseModel):
    is_simple: bool = Field(description="True if this is a simple, direct search query")
    confidence: float = Field(description="Confidence level (0.0-1.0) in the classification")
    reasoning: str = Field(description="Brief explanation of the classification")

# --- Specialist Agents ---
QueryClassifierAgent = Agent(
    name="QueryClassifierAgent",
    model=AgentConfig.ORCHESTRATOR_MODEL,
    description="Determines if a user query is simple enough for fast-path processing.",
    instruction=(
        "You are a query complexity classifier. Analyze the user's request and determine if it's a simple, direct search that can be handled with a single search operation.\n\n"
        "**SIMPLE QUERIES (use fast path):**\n"
        "- Direct activity searches: 'swimming classes for kids', 'yoga classes', 'hockey for 8 year old'\n"
        "- Basic filtering: 'swimming in Burnaby', 'classes on Saturday'\n"
        "- Age-specific requests: 'activities for 5 year old'\n\n"
        "**COMPLEX QUERIES (use full workflow):**\n"
        "- Back-to-back requests: 'swimming then gymnastics'\n"
        "- Multi-step planning: 'find activities that work with my schedule'\n"
        "- Comparison requests: 'which is better for my child'\n"
        "- Abstract planning: 'help me plan my child's week'\n\n"
        "Set `is_simple` to `true` if the query is a straightforward search, `false` if it requires complex orchestration."
    ),
    output_schema=QueryClassification,
)

FastSearchAgent = Agent(
    name="FastSearchAgent",
    model=AgentConfig.ORCHESTRATOR_MODEL,
    description="Performs a single, fast search for simple queries without confirmation steps.",
    instruction=(
        "You are a fast activity search agent. For simple queries, you perform a direct search and return formatted results.\n\n"
        "**PROCESS:**\n"
        "1. **Parse the request** into search arguments for `raw_activity_search`\n"
        "2. **Call `raw_activity_search`** with these arguments:\n"
        "   - `query`: The search query (e.g., 'swimming classes')\n"
        "   - `filters`: A dictionary with ONLY these valid keys: age, location, day_of_week, max_price, date, is_open, name_contains\n"
        "   - `find_back_to_back`: Boolean (usually false unless specifically requested)\n"
        "3. **Format the results** into a helpful response\n\n"
        "**SEARCH STRATEGY:** Be adaptive to user intent:\n"
        "- For general requests ('swimming classes for 5 year olds'), use broad search without forcing 'beginner' terms\n"
        "- Only add 'beginner' terms when user explicitly mentions beginner/intro/starter\n"
        "- Let the search return all age-appropriate options and organize them in the response\n\n"
        "**IMPORTANT:** The filters object must ONLY contain the valid keys listed above. Do NOT add custom fields like 'beginner' or 'skill_level'.\n\n"
        "**EXAMPLE:**\n"
        "User: 'any swimming classes for 5 year olds in new west?'\n"
        "Call: raw_activity_search(query='swimming classes', filters={'age': 5, 'location': 'new west'}, find_back_to_back=false)"
    ),
    tools=[
        raw_activity_search
    ],
    output_key="fast_search_results"
)

# --- Specialist Agents ---
SearchPlannerAgent = Agent(
    name="SearchPlannerAgent",
    model=AgentConfig.ORCHESTRATOR_MODEL,
    description="Analyzes a user's request and creates a step-by-step plan for how it will find the answer using its internal tools.",
    instruction=(
        "You are an expert activity planning assistant. Your job is to create a concise, 1-2 sentence, human-readable plan that describes the actions YOU WILL TAKE using your internal tools. "
        "Do NOT suggest external actions like 'search online' or 'contact facilities'. "
        "Your plan must state that you will search your specialized activity database.\n\n"
        "--- CORRECT EXAMPLE ---\n"
        "User Request: 'any swimming classes for 5 year olds in new west?'\n"
        "Your Plan: 'Okay, I will search my activity database for swimming lessons suitable for a 5-year-old in New Westminster. Does that sound good?'\n\n"
        "--- INCORRECT EXAMPLE ---\n"
        "Plan: '1. Search online... 2. Check websites...'\n\n"
        "Your output is ONLY the single-paragraph plan text to be shown to the user for approval."
    ),
    # This agent should NOT have an output_key. Its return value will be captured by the AgentTool.
)

ArgumentGeneratorAgent = Agent(
    name="ArgumentGeneratorAgent",
    model=AgentConfig.ORCHESTRATOR_MODEL,
    description="Generates the precise JSON arguments for the `raw_activity_search` tool.",
    instruction=(
        "You are an expert at creating structured search queries. Based on the user's request, generate a single JSON object with the exact arguments (`query`, `filters`, `find_back_to_back`) for the `raw_activity_search` tool.\n\n"
        "**CRITICAL REASONING RULES:**\n"
        "1. **General Age-Based Queries:** For general requests like 'swimming classes for 5 year old' or 'any swimming classes for kids':\n"
        "   - Use a broad query like 'swimming classes' without forcing 'beginner' terms\n"
        "   - Only set age filter, let the search return all age-appropriate options\n"
        "   - Do NOT set name_contains filter unless user specifically mentions level/difficulty\n\n"
        "2. **Explicit Beginner Requests:** Only when user explicitly mentions 'beginner', 'intro', 'starter', 'first time':\n"
        "   - Add 'beginner introduction starter' to the query\n"
        "   - Set `filters.name_contains` to 'beginner intro starter level 1'\n\n"
        "3. **Level-Specific Requests:** If the user asks for a specific level (e.g., 'level 5', 'advanced'):\n"
        "   - Include that exact level in the `query` string\n"
        "   - Set `filters.name_contains` to match that level (e.g., 'level 5' or 'advanced')\n\n"
        "4. **Back-to-Back Detection:** Set `find_back_to_back` to `true` only if the user explicitly mentions 'back-to-back', 'consecutive', 'one after another', or similar phrasing.\n\n"
        "5. **Filter Extraction:** Extract explicit filters (age, location, day_of_week, max_price, date, is_open) from the user's request.\n\n"
        "**EXAMPLES:**\n"
        "User: 'swimming classes for 5 year old'\n"
        "Response: {\"query\": \"swimming classes\", \"filters\": {\"age\": 5}, \"find_back_to_back\": false}\n\n"
        "User: 'beginner swimming classes for 5 year old'\n"
        "Response: {\"query\": \"swimming classes beginner introduction starter\", \"filters\": {\"age\": 5, \"name_contains\": \"beginner intro starter level 1\"}, \"find_back_to_back\": false}\n\n"
        "User: 'level 5 swimming classes'\n"
        "Response: {\"query\": \"swimming classes level 5\", \"filters\": {\"name_contains\": \"level 5\"}, \"find_back_to_back\": false}"
    ),
    output_schema=SearchArgs,
)

ActivitySynthesizerAgent = Agent(
    name="ActivitySynthesizerAgent",
    model=AgentConfig.RESPONSE_AGENT_MODEL,
    description="Takes raw JSON search data and synthesizes a high-quality answer.",
    instruction=(
        "You are an expert parent activity consultant. You will be given raw activity data in JSON format and the user's original request. "
        "Create a helpful, well-structured response that organizes activities by level/type and includes detailed scheduling information.\n\n"
        "**CRITICAL FORMATTING REQUIREMENTS:**\n"
        "1. **Start with a positive confirmation**: 'Yes, there are several [activity type] available for [age]-year-olds in [location].'\n"
        "2. **Group by Level/Type**: Organize activities by level (e.g., 'Swimming Level 01 - Preschool (Ages 4-6)')\n"
        "3. **For each level, show**:\n"
        "   - Level name with age range in parentheses\n"
        "   - Location: [Facility name]\n"
        "   - Schedule: Every [days] from [start time] - [end time] ([start date] - [end date], [year]) - $[price]\n"
        "4. **Time formatting**: Convert 24-hour to 12-hour format (e.g., 16:00 → 4:00 PM)\n"
        "5. **Date formatting**: Convert YYYY-MM-DD to 'Mon DD - Mon DD, YYYY' format\n"
        "6. **Days formatting**: Convert ['monday', 'wednesday'] to 'Mon, Wed'\n"
        "7. **Price handling**: If price is missing/null, estimate based on similar activities or omit price\n\n"
        "**EXACT EXAMPLE FORMAT TO FOLLOW:**\n"
        "Swimming Level 01 - Preschool (Ages 4-6)\n"
        "Location: təməsew̓txʷ Aquatic and Community Centre\n"
        "Schedule: Every Mon, Wed from 4:00 PM - 4:25 PM (Jun 30 - Jul 28, 2025) - $72.00\n"
        "Schedule: Every Tue, Thu from 4:00 PM - 4:25 PM (Jul 03 - Jul 29, 2025) - $64.00\n\n"
        "**BACK-TO-BACK HANDLING**: If find_back_to_back was requested, identify activities that occur consecutively on the same day and present them as back-to-back options with detailed timing.\n\n"
        "**IMPORTANT**: Use the exact formatting style shown above. Convert all times, dates, and organize by level systematically."
    ),
    input_schema=SynthesizerInput,
)

# --- NEW CONTROLLER AGENT TO MANAGE THE ENTIRE SUB-WORKFLOW ---
# This single agent will handle the entire plan -> confirm -> ... -> synthesize flow.
ActivityPlanAndSearchController = Agent(
    name="ActivityPlanAndSearchController",
    model=AgentConfig.ORCHESTRATOR_MODEL,
    description="Manages the entire workflow of planning, confirming, searching, and synthesizing a response for an activity request.",
    instruction=(
        "You are the main controller for finding activities. You MUST follow these steps in order:\n"
        "1. **Call `SearchPlannerAgent`** with the user's request to get a human-readable `plan`.\n"
        "2. **Call `user_confirmation_tool`** with the `plan` to get user approval. If the user says 'no' or disagrees, stop and apologize.\n"
        "3. **Call `ArgumentGeneratorAgent`** with the user's request to get the precise `tool_arguments`.\n"
        "4. **Call `raw_activity_search`** using the `tool_arguments` from the previous step. This will give you the `raw_search_results`.\n"
        "5. **Call `ActivitySynthesizerAgent`** with the original `user_request` and the `raw_search_results` to generate the `final_answer`.\n"
        "6. **Return the `final_answer`** as your result."
    ),
    tools=[
        # Expose all necessary components as tools to this controller
        AgentTool(agent=SearchPlannerAgent),
        user_confirmation_tool,
        AgentTool(agent=ArgumentGeneratorAgent),
        raw_activity_search,
        AgentTool(agent=ActivitySynthesizerAgent)
    ],
    output_key="final_formatted_answer" # The final result of this entire flow
)

# --- WORKFLOW DEFINITIONS (Simplified) ---

# The main activity workflow is now just the single, powerful controller agent.
ActivityWorkflow = SequentialAgent(
    name="ActivityWorkflow",
    sub_agents=[
        ActivityPlanAndSearchController
    ]
)

# --- (The rest of the file: GeneralInfoAgent, ResponseAgent, OrchestratorAgent, root_agent remains the same) ---
GeneralInfoAgent = Agent(
    name="GeneralInfoAgent",
    model=AgentConfig.SEARCH_AGENT_MODEL,
    description="Answers general questions by searching the web.",
    output_key="general_info_results",
    tools=[google_search]
)

ResponseAgent = Agent(
    name="ResponseAgent",
    model=AgentConfig.RESPONSE_AGENT_MODEL,
    description="Presents the final answer to the user.",
    instruction=(
        "You are the final response agent. Your job is to present the information found by other agents to the user in a clear and friendly manner. "
        "Check the session state for 'final_formatted_answer' or 'general_info_results'. "
        "Present the content of whichever key is available exactly as it is without adding any conversational filler."
    )
)

SmartOrchestratorAgent = Agent(
    name="parent_activity_assistant_smart_orchestrator",
    model=AgentConfig.ORCHESTRATOR_MODEL,
    description="A smart dispatcher that uses fast-path optimization for simple queries.",
    instruction=(
        "You are an intelligent dispatcher with fast-path optimization. Follow this process:\n\n"
        "1. **For general questions** (not about finding activities): delegate to 'GeneralInfoAgent'\n\n"
        "2. **For activity searches**: \n"
        "   a. **Call `QueryClassifierAgent`** to determine if the query is simple or complex\n"
        "   b. **If simple** (`is_simple` = true): delegate to 'FastSearchAgent' for immediate results\n"
        "   c. **If complex** (`is_simple` = false): delegate to 'ActivityWorkflow' for full orchestration\n\n"
        "**EXAMPLES:**\n"
        "- 'swimming for 5 year old' → Simple → FastSearchAgent\n"
        "- 'swimming then gymnastics back-to-back' → Complex → ActivityWorkflow\n"
        "- 'what is the weather today?' → General → GeneralInfoAgent"
    ),
    tools=[
        AgentTool(agent=QueryClassifierAgent)
    ],
    sub_agents=[
        FastSearchAgent,
        ActivityWorkflow,
        GeneralInfoAgent
    ]
)

root_agent = SequentialAgent(
    name="MainWorkflow",
    sub_agents=[
        SmartOrchestratorAgent,
        ResponseAgent
    ]
)

logger.info("✅ Enhanced, multi-step reasoning workflow initialized.")